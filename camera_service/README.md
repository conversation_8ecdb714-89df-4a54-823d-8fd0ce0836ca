# Windows Camera Service

本服务为在 WSL2 环境或其他网络服务中访问连接在 Windows 主机上的 USB 摄像头提供了桥梁。它是一个基于 FastAPI 构建的轻量级 Web 服务器，通过 HTTP 暴露摄像头功能。

## 目的

WSL2 无法直接访问大多数主机硬件，包括 USB 摄像头。本服务运行在 Windows 主机上，捕获摄像头画面，并通过本地网络进行流式传输，使位于 WSL2 内的 Django 应用能够使用。

## 功能

-   **实时视频流**：提供低延迟的 MJPEG 视频流。
-   **单帧捕获**：提供简单的 API 接口以捕获高质量静态图像。
-   **轻量级**：基于 FastAPI 和 Uvicorn，开销极小。
-   **易于使用**：简单的部署流程和清晰的 API 接口。

## API 接口

-   `GET /`：健康检查接口。返回服务和摄像头状态。
-   `GET /video_feed`：提供实时 MJPEG 视频流。可直接在支持的浏览器（如 Chrome 或 Firefox）中查看。
-   `GET /capture`：捕获单帧并以 `image/jpeg` 格式返回。

## 安装与使用

本服务应在**连接有 USB 摄像头的 Windows 主机**上运行。

### 1. 安装依赖

建议使用 Python 虚拟环境。

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows 命令提示符：
# venv\Scripts\activate.bat
# PowerShell：
# venv\Scripts\Activate.ps1

# 安装依赖包
pip install -r requirements.txt
```

### 2. 运行服务

依赖安装完成后，可直接运行服务：

```bash
python main.py
```

服务将启动在 `http://0.0.0.0:8080`，可被本地网络（包括 WSL2 实例）访问。

### 3. 从 WSL2 访问

要让 WSL2 中的 Django 应用访问本服务，需要在 Django 后端配置 Windows 主机的 IP 地址。

通常可通过在 Windows 命令提示符中运行 `ipconfig` 获取主机 IP 地址。查找与你主要网络适配器（如“以太网适配器”或“无线局域网适配器 Wi-Fi”）关联的 IP 地址。

然后，在 Django 项目的 `.env` 文件中添加一个变量，指向本服务：

```env
# backend 目录下的 .env 文件
# 注意：现在后端使用JSON格式来管理多个摄像头服务。
# 你需要将此服务的URL添加到该JSON配置中。
# 例如: CAMERA_SERVICES_JSON={"main_gate_cam": "http://*************:8080", "back_door_cam": "http://*************:8080"}
CAMERA_SERVICES_JSON={"your_camera_name": "http://*************:8080"}
```

Django 应用即可使用该 URL 访问 `/capture` 和 `/video_feed` 接口。