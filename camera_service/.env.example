# Environment variables for the Camera Service

# This is the full URL to the internal token verification endpoint in your Django backend.
# You must use the IP address of your WSL2 instance, not localhost.
#
# HOW TO FIND THE WSL2 IP:
# 1. Open a WSL2 terminal (where you run Django).
# 2. Run the command: `hostname -I`
# 3. This will output the IP address of your WSL2 instance.
#
# Replace <YOUR_WSL2_IP_ADDRESS> with the IP you found.
# Example: DJANGO_VERIFY_URL=http://************:8000/api/internal/verify_stream_token/
DJANGO_VERIFY_URL=http://<YOUR_WSL2_IP_ADDRESS>:8000/api/internal/verify_stream_token/

# This MUST be the exact same secret key that you have in your Django backend's .env file.
INTERNAL_SECRET_KEY=a-very-strong-and-random-secret-for-internal-use