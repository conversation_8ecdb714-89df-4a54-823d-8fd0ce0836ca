#!/usr/bin/env python3
"""
摄像头服务端点测试脚本
"""

import requests
import json

def test_root_endpoint():
    """测试根端点"""
    print("🔍 测试根端点 (/)")
    try:
        response = requests.get("http://localhost:8080/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print("✅ 根端点正常")
            print(f"服务状态: {data.get('status')}")
            print(f"摄像头可用: {data.get('camera_available')}")
            print(f"可用端点: {json.dumps(data.get('endpoints', {}), indent=2)}")
        else:
            print(f"❌ 根端点异常: {response.text}")
    except Exception as e:
        print(f"❌ 根端点测试失败: {e}")

def test_video_feed_without_token():
    """测试没有token的video_feed端点"""
    print("\n🔍 测试video_feed端点（无token）")
    try:
        response = requests.get("http://localhost:8080/video_feed")
        print(f"状态码: {response.status_code}")
        if response.status_code == 400:
            data = response.json()
            print("✅ 正确返回400错误")
            print(f"错误信息: {data.get('message')}")
            print(f"示例: {data.get('example')}")
        else:
            print(f"❌ 意外的响应: {response.text}")
    except Exception as e:
        print(f"❌ video_feed测试失败: {e}")

def test_capture_endpoint():
    """测试capture端点"""
    print("\n🔍 测试capture端点")
    try:
        response = requests.get("http://localhost:8080/capture")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ capture端点正常")
            print(f"内容类型: {response.headers.get('content-type')}")
            print(f"图像大小: {len(response.content)} bytes")
            
            # 保存测试图像
            with open("test_capture.jpg", "wb") as f:
                f.write(response.content)
            print("测试图像已保存为: test_capture.jpg")
        else:
            print(f"❌ capture端点异常: {response.text}")
    except Exception as e:
        print(f"❌ capture测试失败: {e}")

def main():
    """主测试函数"""
    print("🧪 摄像头服务端点测试")
    print("=" * 40)
    
    test_root_endpoint()
    test_video_feed_without_token()
    test_capture_endpoint()
    
    print("\n" + "=" * 40)
    print("📋 测试完成")
    print("\n💡 说明:")
    print("- 如果看到HTTP 422错误，说明有人直接访问了/video_feed而没有token")
    print("- 正常的video_feed访问需要通过前端获取带token的URL")
    print("- capture端点可以直接访问，用于人脸验证")

if __name__ == "__main__":
    main()
