<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Service Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Aria<PERSON>,
            'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
            'Noto Color Emoji';
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
        }
        h1 {
            color: #1890ff;
            margin-bottom: 24px;
        }
        .camera-container {
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background-color: #fff;
            padding: 16px;
            width: 672px; /* 640px + 16px*2 padding */
        }
        img {
            width: 640px;
            height: 480px;
            border-radius: 4px;
            display: block;
            background-color: #000;
        }
        .info {
            margin-top: 16px;
            font-size: 14px;
            color: #888;
            text-align: center;
        }
        code {
            background-color: #eef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        }
    </style>
</head>
<body>
    <h1>📷 Camera Service Live Feed Test</h1>
    <div class="camera-container">
        <img id="video-feed" src="http://localhost:8080/video_feed" alt="Loading Camera Feed...">
    </div>
    <p class="info">
        If you see a live video above, the camera service is working correctly! <br>
        The image is being streamed from the <code>/video_feed</code> endpoint.
    </p>

    <script>
        const img = document.getElementById('video-feed');
        img.onerror = function() {
            const container = document.querySelector('.camera-container');
            container.innerHTML = `
                <div style="width: 640px; height: 480px; background-color: #000; color: #f00; display: flex; flex-direction: column; align-items: center; justify-content: center; border-radius: 4px;">
                    <p style="font-size: 24px; margin: 0;">Connection Error</p>
                    <p style="margin-top: 16px;">Could not connect to the camera service at <code>http://localhost:8080/video_feed</code>.</p>
                    <p>Please make sure the camera_service is running.</p>
                </div>
            `;
        };
    </script>
</body>
</html>