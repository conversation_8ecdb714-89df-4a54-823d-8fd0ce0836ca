# 更新日志

记录更新内容，提交的时候无需将日期一起放入commit当中。
 
**更新类型说明：**
- 类型(type)：
  - feat: 功能变更（新增/修改功能）
  - remove: 删除内容（文件/代码/资源）
  - refactor: 代码重构（结构调整/优化）
  - docs: 文档相关（README/注释/手册）  
  - fix: 问题修复（BUG/逻辑错误）
  - ui: 界面调整（布局/样式/交互）
  - chore: 日常维护（构建/配置/清理）
  - test: 测试相关（用例/框架）

项目版本号管理说明：
本项目采用语义化版本控制（Semantic Versioning）的概念进行版本管理，并结合自定义的递增规则，以反映项目的开发进度和功能变化。版本号格式为：V{MAJOR}.{MINOR}.{PATCH}.{BUILD}。

**版本号组成说明：**

*   **MAJOR (主版本号)**：
    *   当进行了**不兼容的 API 修改**或**引入了重大新功能**时，主版本号会递增。这通常意味着需要对依赖此版本的外部组件或用户进行修改才能兼容。
    *   **递增时机：** 当有全局性的、底层架构的、或对外部接口/用户体验有重大影响的不兼容变更时。

*   **MINOR (次版本号)**：
    *   当以**向下兼容**的方式添加了**新功能**或进行了**功能性改进**时，次版本号会递增。
    *   **递增时机：** 当添加了新的功能模块、新的 API 端点、或者对现有功能进行了显著的、向下兼容的增强时。

*   **PATCH (补丁版本号)**：
    *   当进行了**向下兼容的问题修复**时，补丁版本号会递增。
    *   **递增时机：** 当修复了 BUG、逻辑错误、UI 问题等不引入新功能且不破坏兼容性的变更时。

*   **BUILD (构建版本号)**：
    *   此版本号用于区分在**同一个 PATCH 版本号下**进行的**多次提交**。通常用于**同一天**内进行的小幅迭代、测试或连续的 BUG 修复，但这些变更不足以独立提升 PATCH 版本号。
    *   **递增时机：** 当在同一天内，对代码进行了多次小幅度的修改、调试或连续的提交，这些变更属于同一个 PATCH 版本范围，且希望区分每次具体的提交时。 BUILD 版本号会从 `1` 开始递增。

**版本号递增规则总结：**

1.  **MAJOR 递增时：** MINOR、PATCH 和 BUILD 版本号**重置为 0**。
2.  **MINOR 递增时：** PATCH 和 BUILD 版本号**重置为 0**。
3.  **PATCH 递增时：** BUILD 版本号**重置为 0**。
4.  **BUILD 递增时：** 仅 BUILD 版本号递增。

**示例：**

*   `V1.0.0` -> `V2.0.0` (重大不兼容变更)
*   `V1.0.0` -> `V1.1.0` (新增向下兼容功能)
*   `V1.0.0` -> `V1.0.1` (修复向下兼容 BUG)
*   `V1.0.1` -> `V1.0.1.1` (同一天在 V1.0.1 版本下进行了小改动)
*   `V1.0.1.1` -> `V1.0.1.2` (同一天在 V1.0.1 版本下又进行了小改动)
*   `V1.0.1.2` -> `V1.0.2` (修复了另一个 BUG，或进入了新的一天进行 PATCH 修复)
*   `V1.0.2` -> `V1.1.0` (添加了新的向下兼容功能)

---

**V0.29.0 feat(esp32): 新增RGB LED状态指示和OLED英文显示支持**

**日期**: 2025-07-19

**类型**: `feat`
**范围**: `esp32`

**说明**:
为ESP32智能门禁设备新增了RGB LED彩色状态指示功能，并优化了OLED显示为英文界面，解决了中文字库显示乱码问题。通过颜色直观地反映门禁系统的不同状态，提升了用户体验和设备可用性。

**变更内容**:

*   **1. 新增RGB LED状态指示功能 (`feat`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/config.h`
    *   **变更**:
        *   新增RGB LED引脚配置 `RGB_LED_PIN 48`
        *   新增RGB LED功能开关 `ENABLE_RGB_LED`
        *   定义RGB颜色常量：红、绿、黄、蓝、白、关闭
        *   新增RGB LED亮度和更新间隔配置

*   **2. 实现RGB LED控制逻辑 (`feat`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/access_control_device.ino`
    *   **变更**:
        *   新增Adafruit NeoPixel库支持
        *   实现RGB LED初始化、颜色设置、状态更新函数
        *   集成RGB LED到门禁状态流程中
        *   添加RGB LED测试功能和详细调试日志

*   **3. RGB LED状态映射逻辑 (`feat`, `esp32`)**:
    *   **状态颜色定义**:
        *   🟡 **黄色**: 门关闭/待机状态（默认状态）
        *   🟢 **绿色**: 门开启状态（访问授权/手动开门）
        *   🔴 **红色**: 验证失败状态（访问拒绝）
        *   🔵 **蓝色**: 系统状态（WiFi连接中）
    *   **自动状态切换**: 临时状态3秒后自动恢复到待机状态

*   **4. OLED显示英文化改进 (`fix`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/config.h`
    *   **变更**:
        *   将所有OLED显示消息改为英文避免乱码
        *   设备名称改为 "Main Gate"
        *   系统消息全部英文化：Device Ready, Card Detected, Access Granted等

*   **5. 新增库依赖和文档 (`docs`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/libraries.txt`
    *   **变更**:
        *   新增Adafruit NeoPixel库依赖说明
        *   添加RGB LED硬件连接指南
        *   提供完整的库安装和配置说明

*   **6. 编译测试指南 (`docs`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/compile_test.md`
    *   **变更**:
        *   创建详细的编译前检查清单
        *   提供常见编译错误解决方案
        *   添加硬件连接验证和调试指南

*   **7. 更新项目文档 (`docs`, `esp32`)**:
    *   **文件**: `esp32_arduino/README.md`
    *   **变更**:
        *   更新功能特性列表，新增RGB状态指示
        *   添加RGB LED引脚配置和使用说明
        *   更新硬件要求和故障排除指南

**技术改进**:
*   **硬件扩展**: 支持WS2812B/NeoPixel RGB LED (GPIO48)
*   **状态可视化**: 通过颜色直观显示门禁系统状态
*   **用户体验**: OLED英文显示解决中文乱码问题
*   **代码结构**: 模块化的RGB LED控制函数和状态管理

**功能增强**:
*   **实时状态反馈**: RGB LED提供即时的视觉状态指示
*   **多重指示**: LED指示灯 + RGB LED + OLED显示的三重状态反馈
*   **自动恢复**: 临时状态自动恢复到待机状态
*   **调试支持**: 详细的RGB状态日志和测试功能

**硬件兼容性**:
*   **RGB LED**: 支持WS2812B/NeoPixel标准RGB LED
*   **引脚配置**: GPIO48专用于RGB LED控制
*   **电源要求**: 支持3.3V或5V RGB LED模块
*   **库依赖**: 新增Adafruit NeoPixel库支持

---

**V0.28.1 fix(camera): 修复摄像头服务HTTP 422错误并改进错误处理**

**日期**: 2025-07-19

**类型**: `fix`
**范围**: `camera`

**说明**:
修复了摄像头服务中因直接访问 `/video_feed` 端点而没有提供必需的 `token` 参数导致的HTTP 422错误。通过添加自定义异常处理器和改进错误信息，为用户提供更友好的错误提示和使用指导。

**变更内容**:

*   **1. 修复HTTP 422错误处理 (`fix`, `camera`)**:
    *   **文件**: `camera_service/main.py`
    *   **变更**:
        *   添加了自定义的 `RequestValidationError` 异常处理器
        *   将缺少token参数的错误从HTTP 422改为更语义化的HTTP 400
        *   为 `/video_feed` 端点的验证错误提供详细的错误信息和使用示例
        *   添加了token参数的详细描述和文档说明

*   **2. 改进根端点信息展示 (`fix`, `camera`)**:
    *   **文件**: `camera_service/main.py`
    *   **变更**:
        *   扩展了根端点 `/` 的响应信息，包含所有可用端点列表
        *   添加了端点使用说明，明确指出video_feed需要token
        *   提供了更完整的服务状态信息

*   **3. 增强调试和日志功能 (`fix`, `camera`)**:
    *   **文件**: `camera_service/main.py`
    *   **变更**:
        *   在video_feed端点添加了token验证过程的详细日志
        *   改进了错误信息的可读性和调试价值
        *   添加了请求处理过程的状态跟踪

*   **4. 新增端点测试工具 (`test`, `camera`)**:
    *   **文件**: `camera_service/test_endpoints.py`
    *   **变更**:
        *   创建了专门的端点测试脚本
        *   包含根端点、video_feed（无token）、capture端点的完整测试
        *   提供了故障排除建议和使用说明

**修复效果**:
*   **HTTP 422错误**: 现在返回HTTP 400和友好的错误信息，而不是难以理解的验证错误
*   **用户体验**: 提供了清晰的错误信息、使用示例和端点文档
*   **调试能力**: 增强了日志记录，便于问题诊断和监控

---

**V0.28.0 feat(core): 恢复完整的NFC+人脸双重验证流程并优化识别阈值**

**日期**: 2025-07-19

**类型**: `feat`
**范围**: `core`

**说明**:
恢复了完整的智能门禁双重验证功能，将之前为调试目的而禁用的人脸识别步骤重新集成到NFC刷卡验证流程中。同时基于实际测试结果优化了人脸识别的相似度阈值，提高了同一用户的识别成功率，实现了真正安全可靠的门禁系统。

**变更内容**:

*   **1. 恢复完整的人脸验证流程 (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   修复了 `get_face_embedding_for_user` 方法中的数据类型转换问题，正确将数据库中的bytes类型转换为numpy数组
        *   恢复了完整的人脸验证逻辑，替换了之前被注释的临时绕过代码
        *   集成了新的多摄像头架构，根据设备绑定的摄像头服务动态获取实时图像
        *   改进了访问快照保存机制，使用唯一文件名并保存到永久目录
        *   完善了错误处理和日志记录，涵盖各种验证失败场景

*   **2. 优化人脸识别相似度阈值 (`fix`, `backend`)**:
    *   **文件**: `backend/face_db/services.py`
    *   **变更**:
        *   基于实际测试数据将人脸验证阈值从 0.50 调整为 0.60
        *   解决了同一用户因阈值过严格导致的验证失败问题（测试距离值：0.5575, 0.5688）
        *   在安全性和可用性之间找到更好的平衡点，适应光照、角度等环境变化

**完整验证流程**:
1. **NFC卡片扫描** → 验证用户身份和权限
2. **检查人脸数据** → 确保用户已注册人脸信息
3. **获取摄像头服务** → 根据设备绑定的摄像头服务
4. **捕获实时图像** → 调用摄像头服务的 `/capture` 端点
5. **人脸比对验证** → 使用DeepFace进行相似度比较
6. **保存访问快照** → 记录验证过程的图像到 `media/access_snapshots/`
7. **记录访问日志** → 完整的审计追踪和失败原因记录
8. **实时通知** → 通过WebSocket广播验证结果

**技术改进**:
*   **数据类型安全**: 修复了人脸特征向量的类型转换问题
*   **架构适配**: 完美集成了新的多摄像头服务架构
*   **快照管理**: 实现了永久化的访问快照保存机制
*   **阈值优化**: 基于实测数据科学调整识别阈值

**安全增强**:
*   **双重验证**: NFC身份验证 + 人脸生物识别验证
*   **防伪造**: 实时图像捕获防止照片攻击
*   **审计追踪**: 完整的访问记录和快照保存
*   **容错处理**: 全面的错误处理和失败原因记录

---

**V0.27.1 fix(backend): 修复设备摄像头绑定信息页面刷新后丢失的问题**

**日期**: 2025-07-19

**类型**: `fix`
**范围**: `backend`

**说明**:
修复了一个关键的数据一致性问题：设备绑定摄像头后，页面刷新会导致绑定信息丢失。问题根源在于WebSocket实时更新和初始数据加载使用了不同的数据序列化方法，导致摄像头绑定字段在页面刷新时未能正确传递到前端。

**变更内容**:

*   **1. 修复WebSocket Consumer中的设备数据序列化 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   在 `device_to_dict` 方法（第487行）中添加了缺失的 `camera_service_name` 字段
        *   在 `sync_device_to_dict` 方法（第648行）中添加了缺失的 `camera_service_name` 字段
        *   确保了WebSocket实时更新和初始数据加载的数据结构完全一致

**问题分析**:
*   **绑定时正常显示**: 使用 `views.py` 中的 `device_to_dict` 函数通过WebSocket更新，包含 `camera_service_name` 字段
*   **刷新后丢失**: 使用 `consumers.py` 中的 `sync_device_to_dict` 函数获取初始数据，缺少 `camera_service_name` 字段
*   **数据库正常**: 绑定信息确实已保存到数据库，问题出现在数据传输层

**修复效果**:
*   **数据一致性**: WebSocket更新和初始数据加载现在使用相同的字段结构
*   **持久化显示**: 设备摄像头绑定信息在页面刷新后能够正确保持和显示
*   **用户体验**: 消除了用户困惑，确保绑定操作的可靠性和可预期性

---

**V0.27.0 feat(core): 重构视频流架构并新增设备摄像头绑定功能**

**日期**: 2025-07-19

**类型**: `feat`
**范围**: `core` (涉及前端、后端和摄像头服务)

**说明**:
本次更新是一次重大的架构升级。为了解决视频流传输中的安全和网络问题，我们废弃了原有的后端代理方案，并设计、实现了一套全新的、基于一次性安全令牌（Token）的视频流授权机制。同时，在此基础上，为管理员新增了将摄像头服务绑定到具体设备的功能。

**变更内容**:

*   **1. 实现全新安全视频流架构 (`feat`, `refactor`)**:
    *   **后端**:
        *   **文件**: `backend/access_control/views.py`, `backend/access_control/urls.py`, `backend/access_control/models.py`
        *   **变更**:
            *   移除了旧的 `video_feed_proxy` 视图和URL，彻底废弃了通过Django代理视频流的方式。
            *   新增 `VideoStreamToken` 模型 (`migrations/0005_...`)，用于生成和管理短生命周期的一次性访问令牌。
            *   新增 `GenerateVideoStreamURL` API端点，供前端请求一个带有安全令牌的、指向真实摄像头服务的视频URL。
            *   新增 `VerifyVideoStreamToken` 内部API端点，供摄像头服务在提供视频流前，向Django后端验证令牌的有效性。
    *   **摄像头服务**:
        *   **文件**: `camera_service/main.py`
        *   **变更**:
            *   重构了 `/video_feed` 端点，现在它会先从请求中提取令牌，然后通过内部API调用Django后端的 `VerifyVideoStreamToken` 进行验证，只有验证通过后才返回视频流。
    *   **前端**:
        *   **文件**: `frontend/src/components/admin/LiveCameraFeed.tsx`
        *   **变更**:
            *   完全重写了实时视频组件的逻辑。现在它会先向后端请求安全的视频URL，然后再将获取到的URL赋给视频播放器，而不是直接请求一个固定的代理地址。
    *   **配置**:
        *   **文件**: `backend/.env.example`, `camera_service/.env.example`, `backend/config/settings.py`
        *   **变更**:
            *   引入 `INTERNAL_SECRET_KEY` 环境变量，用于后端与摄像头服务之间的安全通信。
            *   引入 `CAMERA_SERVICES_JSON` 环境变量，用于更灵活地配置摄像头服务地址。

*   **2. 新增设备绑定摄像头功能 (`feat`, `ui`)**:
    *   **后端**:
        *   **文件**: `backend/access_control/views.py`, `backend/access_control/urls.py`
        *   **变更**:
            *   新增 `CameraServiceListAPIView`，用于获取所有可用的摄像头服务列表。
            *   新增 `BindCameraToDeviceAPIView`，用于处理将摄像头绑定到设备的请求。
    *   **前端**:
        *   **文件**: `frontend/src/components/admin/DeviceManagement.tsx`
        *   **变更**:
            *   在设备列表中为每个设备增加了“绑定摄像头”按钮。
            *   点击按钮会弹出一个模态框，其中包含一个下拉列表，异步加载并显示所有可用的摄像头服务。
            *   实现了提交绑定请求的客户端逻辑。

*   **3. 尝试修复持久化BUG (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/views.py`
    *   **变更**:
        *   对 `BindCameraToDeviceAPIView` 视图进行了多次重构，从使用 `.update()` 到 `.save()`，并最终通过隔离WebSocket广播代码来尝试解决绑定信息无法持久化的问题。尽管问题最终仍存在，但代码结构得到了审查和加固。

*   **4. 依赖与配置更新 (`chore`)**:
    *   **文件**: `backend/requirements.txt`, `camera_service/requirements.txt`
    *   **变更**:
        *   为后端和摄像头服务添加了必要的依赖库（如 `python-dotenv`, `httpx` 等）以支持新功能。

**改进效果**:
*   **安全性**: 视频流的访问变得更加安全，不再能被未授权的用户直接访问，每次访问都需要一个动态生成的、一次性的令牌。
*   **功能性**: 管理员现在可以通过UI将摄像头资源分配给不同的设备，为后续的监控功能打下了基础。
*   **可维护性**: 通过引入环境变量，系统配置变得更加清晰和灵活，便于在不同环境中部署。
*   **健壮性**: 新的视频流架构解耦了Django应用和摄像头服务，降低了因视频传输问题导致后端服务不稳定的风险。

---

**V0.26.1 fix(frontend): 修复用户端日志重复及管理员分页问题**

**日期**: 2025-07-19

**类型**: `fix`
**范围**: `frontend`

**说明**:
本次更新主要集中在修复前端的两个核心问题：用户端的“我的访问记录”页面会出现重复日志的BUG，以及管理员端“用户管理”页面的分页器在特定情况下不显示快速跳转功能的问题。同时，对部分UI体验进行了优化。

**变更内容**:

*   **1. 修复用户端日志重复问题 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/UserWebSocketContext.tsx`
    *   **变更**:
        *   在处理 `new_access_log` 的 WebSocket 消息时，增加了与管理员端相同的日志ID去重检查逻辑。
        *   现在，即时收到重复的日志推送，前端也只会渲染一次，彻底解决了“我的访问记录”中出现多条相同记录的问题。

*   **2. 修复管理员端用户管理分页 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserManagement.tsx`
    *   **变更**:
        *   重构了分页（pagination）的状态管理方式，将 `showQuickJumper` 等配置项直接初始化到 state 中。
        *   修改了 `Table` 的 `onChange` 事件处理器，以正确处理服务端分页模式下的页码和每页数量变更。
        *   此修改确保了在用户数据需要分页时，“Go to Page”（快速跳转）功能可以被稳定地渲染和使用。

*   **3. 优化“我的访问记录”页面UI (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/MyAccessLogs.tsx`
    *   **变更**:
        *   为访问记录表格增加了 `scroll={{ y: '60vh' }}` 属性，使其在内容超出时出现内部滚动条，避免了页面无限增长。
        *   为表格分页器增加了 `showSizeChanger` 和 `showQuickJumper` 选项，允许用户自定义每页显示的条数并进行快速页面跳转。

*   **4. 后端代码清理 (`refactor`, `backend`)**:
    *   **文件**: `backend/access_control/views.py`
    *   **变更**:
        *   移除了一个重复的 `AccessLogSerializer` 导入语句，使代码更加整洁。

**改进效果**:
*   **问题修复**: 彻底解决了用户端日志重复和管理员端分页功能缺失的核心BUG。
*   **体验提升**: 优化了两个页面的表格交互，使其在数据量大时更易于浏览和导航。
*   **代码健壮性**: 改进了“用户管理”页面的状态管理逻辑，使其更加稳健。

---

**V0.26.0 feat(user-dashboard): 新增用户个人访问记录查看功能**

**日期**: 2025-07-18

**类型**: `feat`
**范围**: `user-dashboard`

**说明**:
本次更新为普通用户新增了查看个人访问记录的功能。用户现在可以在自己的仪表盘中实时、只读地查看到所有与自己相关的门禁访问日志，增强了系统的透明度和用户的自主查询能力。

**变更内容**:

*   **1. 后端日志实时推送增强 (`refactor`, `access_control`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   修改了 `log_and_broadcast_access` 方法，在创建访问日志后，除了向管理员广播外，还会检查日志是否关联了具体用户。
        *   如果日志有关联用户，系统会通过 `channel_layer` 将该条日志定向推送到该用户的专属 WebSocket 组（`user_{user.id}`），实现了消息的精准投递。

*   **2. 新增用户端 WebSocket 支持 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/UserWebSocketContext.tsx` (新创建)
    *   **内容**:
        *   创建了一个新的 React Context (`UserWebSocketContext`)，专门用于处理普通用户的 WebSocket 连接。
        *   该 Context 负责连接到后端的 `/ws/user/dashboard/` 端点，接收并管理后端推送的个人访问日志。

*   **3. 新增“我的访问记录”页面 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/pages/MyAccessLogs.tsx` (新创建)
    *   **内容**:
        *   创建了一个新的页面组件，用于展示用户的个人访问记录。
        *   该组件通过 `useUserWebSocket` Hook 消费 `UserWebSocketContext` 中的实时日志数据，并使用 Ant Design 的 `Table` 组件进行展示。
        *   页面为只读模式，不提供任何删除或修改日志的功能，符合设计要求。

*   **4. 集成新功能到用户仪表盘 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/UserDashboard.tsx`, `frontend/src/App.tsx`
    *   **变更**:
        *   在 `UserDashboard.tsx` 中，使用 `UserWebSocketProvider` 包裹整个仪表盘，使其所有子页面都能访问到实时日志数据。
        *   在侧边栏菜单中添加了“我的访问记录”的新菜单项。
        *   在 `App.tsx` 中为 `/dashboard/my-logs` 添加了新的路由配置，指向 `MyAccessLogs` 组件。

**改进效果**:
*   **功能增强**: 为普通用户提供了期待已久的访问记录查询功能，提升了用户体验和满意度。
*   **实时体验**: 用户可以实时看到自己的访问记录，无需刷新页面，体验流畅。
*   **架构优雅**: 后端通过 `channel_layer` 实现了对不同角色的消息精准推送，前端通过 Context 实现了状态的隔离和管理，代码结构清晰，易于维护。

---

**V0.25.4 docs(review): 新增代码审查报告并总结项目架构**

日期：2025-07-18

**类型**: `docs`
**范围**: `review`

**说明**:
本次更新为项目新增了一份全面的代码审查报告 (`docs/REVIEW_REPORT.md`)。该报告系统性地分析了项目的前后端技术栈、代码结构、核心组件以及交互方式，并对整体架构的优缺点进行了总结。

**变更内容**:

*   **1. 新增代码审查报告 (`docs`, `docs`)**:
    *   **文件**: `docs/REVIEW_REPORT.md` (新创建)
    *   **内容**:
        *   **总体架构**: 概述了项目前后端分离、REST API + WebSocket 混合通信的模式。
        *   **后端分析**: 详细分析了 Django 后端的技术栈、模块化应用（`accounts`, `face_db`, `access_control`）的职责划分以及实时通信的实现。
        *   **前端分析**: 详细分析了 React 前端的技术栈、基于组件化的项目结构、路由管理以及通过 Context API 实现的实时状态管理。
        *   **交互方式**: 梳理了 REST API 和 WebSocket 在项目中各自承担的职责和实现方式。
        *   **总结与建议**: 对项目的优点（架构清晰、实时性强、代码规范）进行了总结，并提出了未来可行的改进建议（如引入更专业的状态管理库、迁移生产数据库、加强测试覆盖等）。

**改进效果**:
*   **知识沉淀**: 将对整个项目架构的深入理解固化为文档，为新成员快速上手和未来的项目维护提供了宝贵的参考资料。
*   **提升可维护性**: 清晰的架构文档有助于开发者在后续迭代中保持代码风格和设计模式的一致性。
*   **明确未来方向**: 报告中提出的改进建议为项目未来的优化和演进指明了清晰的方向。

---

**V0.25.3 feat(backend): 暂时禁用人脸识别二次验证**

日期：2025-07-17

**类型**: `feat`
**范围**: `backend`

**说明**:
为配合前端摄像头功能的调试，本次更新暂时禁用了NFC刷卡后的强制人脸识别二次验证流程。现在，系统在NFC刷卡并成功匹配到用户后，将直接授予访问权限，跳过所有与摄像头调用和人脸比对相关的步骤。

**变更内容**:

*   **1. 禁用二次验证逻辑 (`refactor`, `access_control`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   在 `handle_nfc_scan` 方法中，将原有的包含摄像头调用、人脸比对的完整 `try...except` 逻辑块整体注释掉。
        *   保留了原有代码作为注释，方便未来在前端问题解决后快速恢复二次验证功能。

*   **2. 实现NFC直接授权 (`feat`, `access_control`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   在被注释掉的二次验证逻辑块之后，添加了新的直接授权代码。
        *   现在，只要通过NFC卡号能成功查找到用户，系统就会立即发送 `access_granted` 事件给硬件设备。
        *   在访问日志中，成功授权的记录会以 `face_verification_bypassed` 作为失败原因（`failure_reason`）进行标记，以便清晰地追踪哪些访问是在此模式下被授权的。

**改进效果**:
*   **解耦测试**: 使后端的核心NFC授权流程可以独立于前端摄像头功能进行测试和验证。
*   **提升开发效率**: 允许前端在摄像头相关功能尚未完善时，也能正常测试用户登录、NFC设备交互等其他核心功能，避免了开发流程的阻塞。
*   **可恢复性**: 通过注释而非删除代码的方式，确保了未来可以轻松、安全地重新启用完整的人脸识别二次验证功能。

---

**V0.25.2 fix(camera_service): 修复视频流资源无法正确释放的问题**

日期：2025-07-17

**类型**: `fix`
**范围**: `camera_service`

**说明**:
本次更新修复了摄像头微服务 (`camera_service`) 中的一个严重资源泄漏问题。该问题导致在客户端（浏览器）关闭视频流连接后，服务器端的处理循环并未停止，持续占用摄像头资源。这使得后续的访问请求无法获取画面，并可能导致服务进程卡死，无法通过 `Ctrl+C` 正常退出。

**变更内容**:

*   **1. 增加客户端连接状态检测 (`fix`, `camera_service`)**:
    *   **文件**: `camera_service/main.py`
    *   **变更**:
        *   向视频流生成器 `video_stream_generator` 和API端点 `/video_feed` 注入了 FastAPI 的 `Request` 对象。
        *   在 `while True` 循环的每一次迭代中，都通过 `await request.is_disconnected()` 来检查客户端连接是否依然有效。

*   **2. 实现资源自动释放 (`fix`, `camera_service`)**:
    *   **文件**: `camera_service/main.py`
    *   **变更**:
        *   一旦检测到客户端已断开连接，服务器会立即 `break` 循环，从而停止读取和发送视频帧，并自动释放对摄像头的占用。

*   **3. 增加跨域资源共享 (CORS) 支持 (`feat`, `camera_service`)**:
    *   **文件**: `camera_service/main.py`
    *   **变更**:
        *   添加了 `fastapi.middleware.cors.CORSMiddleware` 中间件，并配置为允许所有来源 (`allow_origins=["*"]`) 的请求。
        *   这解决了在浏览器中通过 `file://` 协议打开的 `camera_test.html` 页面无法访问服务资源的问题。

**改进效果**:
*   **稳定性**: 彻底解决了服务在处理多个或连续的视频流请求时卡死的问题，现在可以被正常关闭和重启。
*   **资源管理**: 确保了摄像头资源在客户端断开连接后能够被正确、及时地释放，允许多个客户端先后、正常地访问视频流。
*   **可测试性**: 通过启用CORS，使得独立的HTML测试页面 (`camera_test.html`) 能够直接、方便地对摄像头服务进行功能验证。

---

**V0.25.1 fix(backend): 修复设备状态与时间戳实时更新的逻辑**

日期：2025-07-17

**类型**: `fix`
**范围**: `backend`

**说明**:
本次更新修复了管理员监控页面中设备状态和“最后在线”时间戳更新不及时的问题。此修复确保了设备在重新上线后其状态能被正确更新为“在线”，并且只要设备保持连接，其活跃时间戳就会在前端界面上进行实时同步。

**变更内容**:

*   **1. 修复设备重连后状态不更新的问题 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   在 `process_heartbeat_and_update_status` 方法中增加了逻辑：当收到一个先前被标记为 `offline` 的设备的心跳时，会主动将其状态更新回 `online`。
        *   确保了这种从 `offline` 到 `online` 的状态变更事件会被正确广播到管理员前端。

*   **2. 修复“最后在线”时间戳不刷新的问题 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   重构了 `handle_heartbeat` 方法，移除了原有的 `if status_was_changed` 判断。
        *   现在，系统会在**每一次**收到设备心跳时，都无条件地将包含最新 `last_seen` 时间戳的完整设备信息广播给管理员前端。

**改进效果**:
*   **状态准确性**: 解决了设备断线重连后，前端状态依然错误地显示为 "OFFLINE" 的 BUG。
*   **数据实时性**: 确保了管理员界面上“最后在线”时间戳的实时刷新，能够准确反映设备的最新活跃状态。
*   **监控可靠性**: 提升了管理员监控系统的整体可靠性和数据一致性，确保管理员看到的是最新、最准确的信息。

---

**V0.25.0 feat(fullstack): 实现设备快速掉线检测并重构前端状态管理**

日期：2025-07-17

**类型**: `feat`, `fix`, `refactor`
**范围**: `fullstack`

**说明**:
本次更新通过前后端协同改造，实现了对硬件设备的快速掉线检测，并对前端管理员页面的状态管理架构进行了重构。后端引入了基于心跳的超时巡检机制，解决了因网络异常导致设备状态更新延迟过高的问题。前端则统一了数据源，移除了冗余代码，提升了整体架构的清晰度和健壮性。

**变更内容**:

*   **1. 实现后端心跳超时检测 (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`, `backend/access_control/apps.py`
    *   **变更**:
        *   在 `consumers.py` 中，增强了 `handle_heartbeat` 方法，使其在每次收到心跳时都更新设备的 `last_seen` 时间戳。
        *   在 `apps.py` 中，创建了一个在后台独立线程中运行的异步任务 `check_device_liveness`。该任务每30秒轮询一次，主动将超过30秒未收到心跳的在线设备标记为“离线”，并向前端广播状态更新。
        *   修复了在 `AppConfig.ready()` 中直接启动异步任务导致的 `RuntimeError`。

*   **2. 重构前端设备状态管理 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/DeviceContext.tsx` (已删除), `frontend/src/pages/AdminDashboard.tsx`, `frontend/src/components/admin/DeviceManagement.tsx`, `frontend/src/components/admin/LiveCameraFeed.tsx`
    *   **变更**:
        *   彻底移除了原先通过HTTP轮询获取设备列表的 `DeviceContext`，消除了冗余的数据源和不必要的API请求。
        *   将所有依赖设备列表的组件（`DeviceManagement` 和 `LiveCameraFeed`）全部切换为使用 `useWebSocket` hook，从 `WebSocketContext` 中获取统一的、实时的设备状态数据。
        *   简化了相关组件的内部逻辑，移除了独立的 `loading` 和 `refresh` 状态。

**改进效果**:
*   **检测灵敏度**: 设备掉线状态的检测时间从原先可能长达数分钟的TCP超时，缩短为30-60秒，实现了快速响应。
*   **架构清晰**: 前端架构得到简化，所有实时数据均由 `WebSocketContext` 统一管理，消除了数据不一致的风险，提升了代码的可维护性。
*   **系统健壮性**: 修复了后端服务启动时的运行时错误，并使系统能够主动、稳定地管理设备状态。
*   **性能提升**: 移除了前端不必要的HTTP轮询请求，降低了客户端和服务器的负载。

---

**V0.24.2 fix(backend): 修复管理员无法获取设备列表的问题**

日期：2025-07-17

**类型**: `fix`
**范围**: `backend`

**说明**:
本次更新修复了管理员前端页面在加载时，因请求一个不存在的API端点 (`/api/admin/devices/`) 而导致无法获取设备列表的404错误。通过在后端补全相应的API视图和URL路由，恢复了设备管理功能的正常运作。

**变更内容**:

*   **1. 新增设备列表API视图 (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/views.py`
    *   **变更**: 新增了 `DeviceListAPIView` 类视图。该视图继承自 `generics.ListAPIView`，使用 `DeviceSerializer` 来序列化并返回所有 `Device` 对象的列表，并设置了 `IsAdminUser` 权限，确保只有管理员可以访问。

*   **2. 注册设备列表URL路由 (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/urls.py`
    *   **变更**: 在 `urlpatterns` 中添加了新的路径 `path('admin/devices/', DeviceListAPIView.as_view(), name='admin-device-list')`，将前端请求的URL与新创建的视图正确地关联起来。

**改进效果**:
*   **功能恢复**: 彻底解决了管理员页面因API缺失导致的404错误，设备列表现在可以被正常加载和显示。
*   **API完整性**: 补全了后端缺失的设备查询API，使得后端服务更加完整。
*   **用户体验**: 管理员可以无障碍地使用设备管理和监控功能，提升了后台系统的可用性。

---

**V0.24.1 docs(project): 同步所有文档以反映项目完成状态**

日期：2025-07-17

**类型**: `docs`
**范围**: `project`

**说明**:
在完成所有核心功能（双重验证、动态视频流）的开发后，对整个项目的文档进行了一次全面的同步更新，以确保所有 `README.md`、项目计划和审查报告都能准确反映项目的最终状态和功能。

**变更内容**:

*   **1. 更新根 `README.md` (`docs`, `root`)**:
    *   **文件**: `README.md`
    *   **变更**: 修改了“项目当前状态”部分，声明所有核心功能已完成，不再是“万事俱备，只欠东风”。

*   **2. 更新后端 `README.md` (`docs`, `backend`)**:
    *   **文件**: `backend/README.md`
    *   **变更**: 更新了“功能特性”和“环境配置”部分，明确说明已实现双重验证逻辑，并解释了新的 `CAMERA_SERVICES_JSON` 配置项。

*   **3. 更新前端 `README.md` (`docs`, `frontend`)**:
    *   **文件**: `frontend/README.md`
    *   **变更**: 在“功能特性”中，将“设备管理”更新为“设备与监控”，并添加了对实时视频流功能的描述。

*   **4. 更新摄像头服务 `README.md` (`docs`, `camera_service`)**:
    *   **文件**: `camera_service/README.md`
    *   **变更**: 修改了与后端集成的说明，以反映后端现在使用 `CAMERA_SERVICES_JSON` 来管理多个摄像头。

*   **5. 更新项目审查报告 (`docs`, `docs`)**:
    *   **文件**: `docs/REVIEW_REPORT.md`
    *   **变更**: 将所有“关键缺失”项标记为“已解决”，并更新了“整体评估与建议”部分，确认项目核心功能已全部实现。

*   **6. 更新项目计划书 (`docs`, `docs`)**:
    *   **文件**: `docs/PROJECT_PLAN.md`
    *   **变更**: 更新了“核心逻辑”流程描述，并已将所有“开发路线图”阶段标记为“已完成”。

**改进效果**:
*   **文档一致性**: 确保了代码实现与项目文档之间的完全同步。
*   **易于理解**: 为新接触该项目的开发者提供了准确、最新的项目概览和功能说明。
*   **可维护性**: 准确的文档有助于未来的维护和二次开发。

---

**V0.24.0 feat(frontend): 实现动态视频监控与设备管理集成**

日期：2025-07-17

**类型**: `feat`
**范围**: `frontend`

**说明**:
本次更新在前端管理员控制台实现了动态、可选择的实时视频监控功能，并将其与设备管理列表无缝集成。管理员现在可以在“设备与监控”页面上，通过下拉菜单选择不同的摄像头来查看实时画面，同时下方会显示所有设备的详细状态列表，提供了一个统一的监控和管理视图。

**变更内容**:

*   **1. 创建设备状态管理 (`feat`, `context`)**:
    *   **文件**: `frontend/src/contexts/DeviceContext.tsx`
    *   **变更**: 新增了 `DeviceContext`，用于全局管理设备列表的状态。它负责从后端API获取所有设备数据，并提供给需要的组件。

*   **2. 实现实时视频流组件 (`feat`, `components`)**:
    *   **文件**: `frontend/src/components/admin/LiveCameraFeed.tsx`
    *   **变更**: 创建了 `LiveCameraFeed` 组件。该组件包含一个下拉选择器，用于列出所有已关联摄像头的设备，并根据用户的选择，动态加载和显示对应摄像头的MJPEG视频流。

*   **3. 集成监控与设备列表 (`ui`, `components`)**:
    *   **文件**: `frontend/src/components/admin/DeviceManagement.tsx`
    *   **变更**: 重构了“设备管理”页面。现在该页面顶部是 `LiveCameraFeed` 视频监控组件，下方是一个使用Ant Design `Table` 组件展示的详细设备列表，该列表会实时显示所有设备的状态、位置、关联摄像头等信息。

*   **4. 更新仪表盘布局 (`refactor`, `pages`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**:
        *   将 `DeviceProvider` 添加到全局Context中，为所有子组件提供设备数据。
        *   更新了侧边栏菜单，将“设备管理”重命名为“设备与监控”，以更准确地反映其新功能。

**改进效果**:
*   **一站式监控**: 管理员无需在不同页面间切换，即可同时查看实时视频和设备状态，提升了管理效率。
*   **提升用户体验**: 通过清晰的布局和直观的操作（下拉选择摄像头），使得监控功能易于使用。
*   **代码结构优化**: 通过引入专门的 `DeviceContext` 和模块化的 `LiveCameraFeed` 组件，前端代码结构更加清晰，易于未来扩展和维护。

---

**V0.23.0 feat(backend): 实现动态可选的多摄像头视频流**

日期：2025-07-17

**类型**: `feat`
**范围**: `backend`

**说明**:
本次更新对后端进行了架构升级，以支持在前端动态选择和展示来自不同物理位置（如“主大门”、“后门”）的摄像头实时视频流。此功能极大地提升了系统的监控灵活性和可扩展性。

**变更内容**:

*   **1. 增强设备模型 (`feat`, `models`)**:
    *   **文件**: `backend/access_control/models.py`
    *   **变更**: 为 `Device` 模型添加了 `camera_service_name` 字段。该字段用于将每个门禁设备与其对应的摄像头服务（在配置中定义）进行关联。
    *   **数据库迁移**: 创建并应用了 `0004_device_camera_service_name.py` 迁移文件。

*   **2. 升级为多摄像头配置 (`refactor`, `config`)**:
    *   **文件**: `backend/config/settings.py`, `backend/.env.example`
    *   **变更**: 将原有的单个 `CAMERA_SERVICE_URL` 配置重构为 `CAMERA_SERVICES_JSON`。该配置现在是一个JSON字符串，可以定义一个包含多个命名摄像头URL的字典，从而使系统能够管理多个独立的摄像头服务。

*   **3. 实现动态视频流视图 (`refactor`, `views`)**:
    *   **文件**: `backend/access_control/views.py`
    *   **变更**: 重构了 `video_feed_proxy` 视图。它现在接受一个 `device_id` 参数，并根据此ID查找设备，获取其关联的 `camera_service_name`，然后从配置中找到对应的URL来代理视频流。

*   **4. 更新URL路由 (`feat`, `urls`)**:
    *   **文件**: `backend/access_control/urls.py`
    *   **变更**: 将视频流的URL从静态的 `/api/admin/video_feed/` 更新为动态的 `/api/admin/video_feed/<str:device_id>/`，以支持按设备ID请求视频流。

**改进效果**:
*   **高度可扩展性**: 系统现在可以轻松地通过修改配置文件来添加、删除或更改摄像头，而无需改动代码。
*   **灵活性**: 前端可以根据用户选择，动态请求任何已配置的摄像头的视频流，为实现灵活的监控界面奠定了基础。
*   **架构优化**: 将设备与摄像头解耦，通过配置进行关联，使得系统架构更加清晰和易于维护。

---

**V0.22.0 feat(backend): 实现NFC与人脸识别双重验证**

日期：2025-07-17

**类型**: `feat`
**范围**: `backend`

**说明**:
本次更新实现了核心的二次验证（2FA）功能。在NFC刷卡成功后，系统现在会触发人脸识别流程，作为第二重安全验证。这极大地增强了门禁系统的安全性，从单一的卡片验证升级为“所持”与“生物特征”相结合的双因素认证。

**变更内容**:

*   **1. 重构NFC处理逻辑 (`refactor`, `access_control`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**: 完全重写了 `handle_nfc_scan` 方法。原有的直接授权逻辑被替换为完整的二次验证流程。

*   **2. 集成摄像头服务调用 (`feat`, `access_control`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**: 在 `handle_nfc_scan` 中添加了通过 `requests` 库调用 `camera_service` 的 `/capture` 接口的逻辑，以获取实时的现场照片。

*   **3. 实现人脸验证流程 (`feat`, `access_control`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   新增 `get_face_embedding_for_user` 方法，用于从数据库异步获取用户的预存人脸特征。
        *   调用 `face_db.services.verify_face` 服务，将现场照片与数据库中的特征进行比对。
        *   根据比对结果（成功/失败）向硬件设备发送最终的开门或拒绝指令。

*   **4. 增强日志记录 (`feat`, `access_control`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**: 新增 `log_and_broadcast_access` 辅助函数，并扩展了访问日志的失败原因，现在可以记录 `unknown_card`, `no_face_registered`, `face_mismatch`, `camera_service_error` 等详细状态。

**改进效果**:
*   **安全性显著提升**: 引入了生物识别作为第二验证因素，有效防止了因卡片丢失或被盗用而导致的安全风险。
*   **核心功能闭环**: 完成了项目计划中最关键的业务逻辑，使门禁系统具备了完整的、可用的核心功能。
*   **鲁棒性增强**: 为整个验证流程添加了详细的错误处理和日志记录，覆盖了摄像头服务不可用、用户未注册人脸等多种异常情况。

---

**V0.21.1.1 feat(backend): 修复access_control/urls.py当中的缩进错误**

日期：2025-07-17

**类型**: `fix`
**范围**: `backend`

**说明**:

**变更内容**:

*   **1. 修复access_control/urls.py当中的缩进错误 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/urls.py`
    *   **变更**: 修复了access_control/urls.py当中的缩进错误。

**改进效果**:
*   **代码可读性提升**: 修复了access_control/urls.py当中的缩进错误。

---

**V0.21.1 feat(backend): 集成摄像头服务支持**

日期：2025-07-17

**类型**: `feat`
**范围**: `backend`

**说明**:
本次更新为 Django 后端添加了与新创建的 `camera_service` 进行通信的必要支持。通过引入新的配置、代理视图和URL路由，后端现在具备了从摄像头服务获取数据并将其安全地暴露给前端的能力，为后续实现人脸识别和实时监控功能奠定了基础。

**变更内容**:

*   **1. 添加摄像头服务配置 (`chore`, `config`)**:
    *   **文件**: `backend/.env.example`, `backend/config/settings.py`
    *   **变更**: 在环境配置中新增了 `CAMERA_SERVICE_URL` 变量，用于定义外部摄像头服务的地址，并在 Django 设置中进行读取。

*   **2. 创建视频流代理视图 (`feat`, `access_control`)**:
    *   **文件**: `backend/access_control/views.py`
    *   **变更**: 新增了一个 `video_feed_proxy` 视图。该视图作为一个安全的代理，负责从 `CAMERA_SERVICE_URL` 获取 MJPEG 视频流，并将其转发给前端客户端。这避免了前端直接与摄像头服务通信，统一了请求入口。

*   **3. 添加视频流路由 (`feat`, `access_control`)**:
    *   **文件**: `backend/access_control/urls.py`
    *   **变更**: 为 `video_feed_proxy` 视图添加了新的 URL 路由 `/api/admin/video_feed/`，使其可以通过 API 被前端访问。

**改进效果**:
*   **建立通信桥梁**: 成功打通了 Django 后端（在WSL2中）与 Windows 摄像头服务之间的通信链路。
*   **增强安全性与架构一致性**: 通过代理模式，所有前端请求都将通过 Django 后端，保持了架构的统一性，并为后续添加认证和权限控制提供了便利。
*   **功能准备**: 为下一步在前端展示实时视频流和在后端调用拍照功能做好了充分准备。

---

**V0.21.0 feat(camera): 新增独立的 Windows 摄像头服务**

日期：2025-07-17

**类型**: `feat`
**范围**: `camera`

**说明**:
为了解决在 WSL2 环境中无法直接访问主机 USB 摄像头的问题，本次更新引入了一个全新的独立模块：`camera_service`。这是一个基于 FastAPI 和 OpenCV 的轻量级 Python 服务，旨在运行于 Windows 主机上，通过网络向 WSL2 中的后端应用提供摄像头访问能力。

**变更内容**:

*   **1. 创建摄像头服务模块 (`feat`, `camera`)**:
    *   **目录**: `camera_service/` (新创建)
    *   **变更**: 新增了 `camera_service` 目录，其中包含运行一个独立摄像头服务所需的所有文件。

*   **2. 实现摄像头服务核心逻辑 (`feat`, `camera`)**:
    *   **文件**: `camera_service/main.py` (新创建)
    *   **变更**: 编写了服务核心代码。该服务能够初始化本地摄像头，并提供两个主要的 API 接口：
        *   `GET /video_feed`: 以 MJPEG 格式提供实时视频流。
        *   `GET /capture`: 捕获单张高质量静态图片。

*   **3. 提供服务配置与文档 (`docs`, `camera`)**:
    *   **文件**: `camera_service/requirements.txt` (新创建)
    *   **文件**: `camera_service/README.md` (新创建)
    *   **变更**: 添加了独立的 `requirements.txt` 文件以管理依赖，并创建了详细的 `README.md` 文档，说明了该服务的用途、API 接口、以及在 Windows 上的安装和运行步骤。

**改进效果**:
*   **架构解耦**: 成功将摄像头硬件的访问逻辑从主应用中剥离，形成了一个独立的微服务，解决了跨环境硬件访问的难题。
*   **功能奠基**: 为后端下一步实现“刷卡后拍照”和前端实现“实时视频监控”提供了稳定、可靠的数据源。
*   **可维护性**: 独立的模块和清晰的文档使得该服务易于理解、维护和独立部署。

---

**V0.20.9 docs(project): 完善项目文档，为各模块添加详细 README**

日期：2025-07-17

**类型**: `docs`
**范围**: `project`

**说明**:
本次更新旨在全面提升项目的文档质量和可维护性。通过为项目根目录及各个核心模块（前端、后端、硬件）创建和完善详细的 `README.md` 文件，为开发者提供了清晰的导航和上手指南。同时，更新了审查报告以确保其内容与代码的最新状态保持一致。

**变更内容**:

*   **1. 创建项目总览文档 (`docs`, `project`)**:
    *   **文件**: `README.md` (新创建)
    *   **变更**: 在项目根目录新增了主 `README.md`，内容包括项目简介、整体架构图、技术栈，并提供了指向各子模块文档的快速链接，作为项目的统一入口。

*   **2. 完善各模块文档 (`docs`, `backend`, `frontend`, `esp32`)**:
    *   **文件**: `backend/README.md` (新创建)
    *   **文件**: `frontend/README.md` (内容重写)
    *   **文件**: `esp32_arduino/README.md` (内容重写)
    *   **变更**: 为后端、前端和ESP32固件目录分别创建了详细的说明文档。每个文档都包含了对应模块的功能特性、技术栈、环境配置、依赖安装和快速启动步骤。

*   **3. 同步审查报告 (`docs`, `docs`)**:
    *   **文件**: `docs/REVIEW_REPORT.md` (内容修改)
    *   **变更**: 根据代码审查的最新发现，更新了审查报告。最主要的是修正了原报告中关于前端 WebSocket 功能实现状态的评估，使其准确反映“前端已就绪，瓶颈在后端”的结论。

**改进效果**:
*   **导航清晰**: 开发者可以从根 `README.md` 快速了解项目全貌并导航至特定模块。
*   **上手简单**: 每个模块都有独立的、详细的设置和启动指南，极大地降低了新成员的上手难度。
*   **信息同步**: 项目的文档现在与代码的实际状态保持一致，特别是 `REVIEW_REPORT.md` 准确反映了项目的开发瓶颈，为后续开发指明了方向。

---

**V0.20.8 fix(frontend): 修复管理员登录跳转及清理无用导入**

日期：2025-07-17

**类型**: `fix`, `chore`
**范围**: `frontend`

**说明**:
本次更新是 V0.20.7 版本信息架构重构的后续修复。主要解决了在移除了“仪表盘”功能后，管理员登录成功时仍然尝试跳转到已废弃路由的问题，并清理了因此产生的无用代码，确保了登录流程的顺畅和代码的整洁性。

**核心问题**:
1.  **登录跳转错误**: 在`AdminLoginPage.tsx`中，管理员登录成功后的跳转目标硬编码为`/admin/dashboard`，该路由在V0.20.7版本中已被移除，导致登录后无法正确跳转。
2.  **无用代码残留**: 在`AdminDashboard.tsx`中，由于“仪表盘”菜单项被移除，其对应的`DashboardOutlined`图标导入已不再被使用，成为无用代码。

**变更内容**:

*   **1. 修正登录跳转逻辑 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminLoginPage.tsx`
    *   **变更**: 将管理员登录成功后的`navigate`函数的目标从`/admin/dashboard`修改为`/admin/account`，确保用户被正确引导至其个人账户设置页面。

*   **2. 清理无用图标导入 (`chore`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**: 移除了未被使用的`DashboardOutlined`图标的导入语句，保持了代码的整洁性并消除了编译警告。

**改进效果**:
*   **流程顺畅**: 修复了登录流程中的断点，现在管理员登录后可以无缝跳转到指定的默认页面。
*   **代码质量提升**: 清理了因功能移除而产生的残留代码，提高了代码的可维护性。

---

**V0.20.7 refactor(ui): 重构管理员后台信息架构与导航**

日期：2025-07-17

**类型**: `refactor`, `ui`, `chore`
**范围**: `frontend`

**说明**:
本次更新对管理员后台的整体信息架构和导航进行了全面的优化重构。通过简化导航入口、对菜单进行逻辑分组、并清理废弃的路由和组件，显著提升了界面的直观性、操作效率和代码的可维护性。

**核心问题**:
1.  **导航冗余**: 顶部用户下拉菜单中包含了侧边栏已有的设置入口，造成功能重复。
2.  **结构混乱**: 侧边栏菜单是扁平化结构，未能体现不同功能模块之间的逻辑关系，降低了可发现性。
3.  **存在废弃项**: 侧边栏和路由中仍然存在已无实际功能的“仪表盘”选项，造成界面混乱。
4.  **URL结构不佳**: 管理员后台的URL路径中包含冗余的`/dashboard`层级，不够简洁直观。

**变更内容**:

*   **1. 简化顶部导航 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**: 移除了顶部右侧用户头像下拉菜单中的“个人资料”和“账号设置”入口，仅保留核心的“退出登录”功能。

*   **2. 重构侧边栏菜单 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**:
        *   将侧边栏菜单重构为“个人账户”和“系统管理”两个逻辑分组，并使用分隔线进行视觉区分。
        *   将“个人资料与设置”归入“个人账户”组，其余管理功能归入“系统管理”组。
        *   修复了在重构过程中因`MenuProps`类型定义引发的TypeScript编译错误。

*   **3. 清理废弃路由与组件 (`chore`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`, `frontend/src/App.tsx`
    *   **变更**:
        *   从侧边栏菜单中彻底移除了已无功能的“仪表盘”选项。
        *   从路由配置文件`App.tsx`中删除了与之关联的`index`路由，避免了空页面的存在。

*   **4. 优化URL结构 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/App.tsx`, `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**:
        *   将管理员后台的根路由从`/admin/dashboard`简化为`/admin`。
        *   同步更新了侧边栏所有导航链接的`to`属性，以匹配新的、更简洁的URL结构（如`/admin/users`）。

**改进效果**:
*   **信息架构清晰**: 功能按逻辑明确分组，用户可以更快速、直观地定位到所需模块。
*   **操作效率提升**: 简化了导航层级，减少了冗余入口，优化了用户操作路径。
*   **代码与URL更简洁**: 清理了废弃代码和路由，并采用了更符合RESTful风格的URL结构，提升了项目的可维护性。

---

**V0.20.6 fix(frontend): 全面修复因环境迁移导致的前端连接与资源加载失败问题**

日期：2025-07-17

**类型**: `fix`, `refactor`, `chore`
**范围**: `frontend`

**说明**:
本次更新集中解决了在更换开发环境后，因后端IP地址变化而引发的一系列前端连接与资源加载失败的问题。通过重构和统一URL的处理逻辑，确保了所有API请求和媒体资源（如头像）的加载都能正确通过Vite代理，并修复了一个附带的TypeScript编译配置错误，全面提升了项目的环境适应性和代码健壮性。

**核心问题**:
1.  **API请求失败**: `axios`请求的`baseURL`在开发环境中被错误地硬编码，导致登录等API请求绕过了Vite代理，直接发往了无法访问的`127.0.0.1`地址。
2.  **头像加载失败 (ERR_CONNECTION_REFUSED)**: 后端API返回了包含硬编码本地开发地址的头像URL（如 `http://127.0.0.1:8000/...`）。前端多个组件（包括用户管理和管理员个人设置页面）没有统一处理此URL，直接使用该错误地址请求图片，导致连接被拒绝。
3.  **TypeScript编译错误**: `tsconfig.app.json`中包含了一个当前TypeScript版本不支持的编译器选项`erasableSyntaxOnly`，导致了潜在的编译错误。

**变更内容**:

*   **1. 统一API请求代理逻辑 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/utils/config.ts`
    *   **变更**: 将`API_BASE_URL`在开发环境下的值统一设置为空字符串`''`。此改动强制所有API请求都以相对路径（如`/api/...`）的形式发出，从而确保它们能被Vite的代理规则正确拦截和转发。

*   **2. 加固并统一媒体URL处理 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/utils/config.ts`
    *   **变更**: 重构了`getFullMediaUrl`函数，增加了智能检测逻辑。现在，该函数能够自动识别并移除从后端返回的URL中可能包含的硬编码本地地址（如`http://127.0.0.1:8000`），确保最终生成的是一个可以通过代理访问的、纯净的相对路径。

*   **3. 修复各页面头像加载问题 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserManagement.tsx`, `frontend/src/components/admin/AdminAccountSettings.tsx`
    *   **变更**: 在所有渲染头像的组件中，统一导入并使用了经过加固的`getFullMediaUrl`函数来处理用户头像的URL，确保了URL的正确性和一致性。

*   **4. 清理TypeScript配置 (`chore`, `frontend`)**:
    *   **文件**: `frontend/tsconfig.app.json`
    *   **变更**: 移除了不再支持或非必需的`erasableSyntaxOnly`编译器选项，解决了潜在的编译错误，使配置文件更加整洁。

**改进效果**:
*   **环境适应性**: 项目现在可以无缝地在不同网络环境中迁移，所有对后端的请求（包括API和媒体文件）都能通过Vite代理正确路由。
*   **代码健壮性**: 通过在`getFullMediaUrl`中建立防御性逻辑，前端对后端返回的URL格式具有了更强的容错能力。
*   **一致性与可维护性**: 统一了项目中所有头像的加载方式，遵循了“不要重复自己”（DRY）的原则，降低了未来出现类似问题的风险。

---

**V0.20.5 fix(multi): 修复后端崩溃、Git配置及前端构建错误**

日期：2025-07-17

**类型**: `fix`, `chore`
**范围**: `backend`, `frontend`, `git`

**说明**:
本次更新主要解决了三个核心问题：修复了因无效认证尝试导致后端WebSocket服务崩溃的严重漏洞；修正了`.gitignore`配置，确保`logs`目录在克隆仓库时能被正确创建；修复了前端在构建时因缺少Node.js类型定义而导致的TypeScript编译错误。这些修改增强了项目的稳定性和开发体验。

**核心问题**:
1.  **后端崩溃**: 在`access_control/consumers.py`中，当一个未经授权的用户（如使用无效Token）连接WebSocket并立即断开时，`disconnect`方法会尝试访问一个未被初始化的`group_name`属性，导致`AttributeError`并使服务崩溃。
2.  **Git忽略问题**: `backend/.gitignore`文件错误地将整个`logs/`目录加入忽略列表，导致新克隆的仓库中缺少该目录，若不手动创建则会在运行时报错。
3.  **前端构建失败**: 在`frontend`目录下运行`npm run build`时，`vite.config.ts`文件因无法识别Node.js环境的全局变量`process`而抛出TypeScript编译错误（`TS2580: Cannot find name 'process'`）。

**变更内容**:

*   **1. 修复后端WebSocket崩溃漏洞 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**: 在`AdminMonitorConsumer`和`UserDashboardConsumer`的`disconnect`方法中，增加了`hasattr(self, 'group_name')`的判断逻辑。此改动确保了只有在连接成功建立（即`group_name`属性存在）的情况下，程序才会尝试将连接从频道组中移除，从而避免了因认证失败而引发的崩溃。

*   **2. 修正`.gitignore`配置 (`chore`, `git`)**:
    *   **文件**: `backend/.gitignore`, `backend/logs/.gitkeep`
    *   **变更**:
        *   从`backend/.gitignore`中移除了`logs/`这一行，使得Git不再忽略该目录。
        *   在`backend/logs/`目录下创建了一个空的`.gitkeep`文件，以确保即使该目录为空，也能被Git正确地跟踪和提交。

*   **3. 修复前端构建错误 (`fix`, `frontend`)**:
    *   **文件**: `frontend/tsconfig.node.json`
    *   **变更**: 在`compilerOptions`中添加了`"types": ["node", "vite/client"]`。此配置明确告知TypeScript编译器在处理`vite.config.ts`时加载Node.js和Vite客户端的类型定义，解决了`process`变量未定义的问题。

**改进效果**:
*   **稳定性**: 后端服务变得更加健壮，不再会因为无效的连接尝试而崩溃。
*   **开发体验**: 新开发者克隆项目后，无需任何手动操作即可正常运行，`logs`目录会自动创建。
*   **构建成功**: 前端项目现在可以无错误地通过`npm run build`命令进行构建。

---

**V0.20.4 fix(frontend): 修复多处前端警告及构建错误**

日期：2025-07-17

**类型**: `fix`, `refactor`, `chore`
**范围**: `frontend`

**说明**:
本次更新集中处理了前端代码中存在的多处Ant Design组件使用警告、TypeScript编译错误以及硬编码URL问题。通过对多个组件进行重构和清理，确保了项目能够成功构建，并消除了在浏览器控制台中出现的警告信息，提升了代码质量和可维护性。

**核心问题**:
1.  **构建错误**: 由于存在未使用的导入（`App.tsx`中的`SecuritySettings`）和未使用的函数（`AccessLogManagement.tsx`中的`getAccessTypeTag`），导致`npm run build`命令失败。
2.  **Ant Design警告**:
    *   在`UserDashboard.tsx`中，`Dropdown`组件使用了已废弃的`overlay`属性。
    *   在`WebSocketContext.tsx`中，直接调用静态的`message` API，导致其无法消费`App`组件提供的上下文（Context），产生警告。
3.  **浏览器警告**: 在`UserDashboard.tsx`和`AccountSettings.tsx`中，当用户没有头像时，会向`Avatar`组件的`src`属性传递一个空字符串，这会引起浏览器发出不必要的网络请求并产生警告。
4.  **硬编码URL**: 在`UserDashboard.tsx`和`AccountSettings.tsx`中，存在多处硬编码的后端开发服务器地址(`http://127.0.0.1:8000`)。

**变更内容**:

*   **1. 修复构建错误 (`chore`, `frontend`)**:
    *   **文件**: `frontend/src/App.tsx`, `frontend/src/components/admin/AccessLogManagement.tsx`
    *   **变更**: 移除了`App.tsx`中未使用的`SecuritySettings`导入语句，并删除了`AccessLogManagement.tsx`中未被调用的`getAccessTypeTag`函数，确保项目可以成功编译。

*   **2. 重构组件以修复警告 (`refactor`, `fix`, `frontend`)**:
    *   **文件**: `frontend/src/pages/UserDashboard.tsx`, `frontend/src/components/AccountSettings.tsx`
    *   **变更**:
        *   在`UserDashboard.tsx`中，将`Dropdown`组件的`overlay`属性更新为推荐的`menu`属性。
        *   在两个文件中，都修改了`Avatar`组件的逻辑，确保在头像URL为空时，向`src`属性传递`null`而非空字符串。
        *   引入并使用了`getFullMediaUrl`辅助函数，替换了所有硬编码的头像URL，增强了代码的适应性。

*   **3. 优化Context消费方式 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/WebSocketContext.tsx`, `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**:
        *   重构了`WebSocketProvider`，使其不再直接调用静态`message` API，而是通过`props`接收一个`messageApi`实例。
        *   相应地，在`AdminDashboard.tsx`中，使用`App.useApp()`钩子获取与上下文关联的`message`实例，并将其传递给`WebSocketProvider`，解决了Ant Design的上下文消费警告。

**改进效果**:
*   **构建成功**: 项目现在可以无错误地通过`npm run build`命令进行构建。
*   **控制台清洁**: 消除了浏览器开发工具控制台中所有与本次修复相关的警告信息。
*   **代码质量**: 代码遵循了库的最佳实践，移除了冗余代码和硬编码值，可读性和可维护性更高。
*   **健壮性**: 通过正确处理`null`值和动态生成URL，组件在各种数据情况下的表现更加稳定。

---

**V0.20.3 fix(frontend): 修复因环境变化导致的前后端连接失败问题**

日期：2025-07-16

**类型**: `fix`, `chore`
**范围**: `frontend`

**说明**:
本次更新解决了在更换开发环境（如迁移到新的Win10电脑）后，因WSL2的IP地址变化而导致前端无法连接到后端服务的问题。通过引入`.env`文件和修改Vite的代理配置，将硬编码的后端地址变为动态可配置，大大增强了项目的环境适应性。

**核心问题**:
1.  **代理目标硬编码**: 前端Vite开发服务器的配置文件(`vite.config.ts`)中，代理到后端的API和WebSocket的目标地址被硬编码为`127.0.0.1:8000`。
2.  **环境迁移后连接失败**: 虽然`127.0.0.1`在纯本地开发时有效，但当WSL2的IP地址因更换电脑等原因发生变化时，Vite的代理请求会因为找不到正确的后端地址而失败，导致出现`ECONNREFUSED`连接被拒绝的错误。

**变更内容**:

*   **1. 使Vite配置动态化 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/vite.config.ts`
    *   **变更**: 重构了Vite的配置文件，使其能够使用`loadEnv`函数来读取`.env`文件中的环境变量，并根据这些变量来动态设置代理目标。

*   **2. 新增环境配置文件 (`feat`, `frontend`)**:
    *   **文件**: `frontend/.env` (新增)
    *   **变更**: 创建了一个新的`.env`文件，用于定义`VITE_BACKEND_TARGET`和`VITE_WS_TARGET`两个环境变量。这允许开发者在不修改任何源代码的情况下，轻松指定后端服务的地址。文件内包含了清晰的注释，指导如何获取和配置WSL2的IP地址。

*   **3. 补全类型定义 (`chore`, `frontend`)**:
    *   **文件**: `frontend/package.json`, `frontend/package-lock.json`
    *   **变更**: 添加了`@types/node`作为开发依赖，以解决在`vite.config.ts`中使用`process.cwd()`时出现的TypeScript类型错误。

**改进效果**:
*   **环境适应性**: 项目现在可以轻松地在不同的开发环境中迁移和运行，只需修改`.env`文件即可适应新的网络配置。
*   **开发者体验**: 简化了在复杂网络环境下（如局域网调试）的配置流程，提高了开发效率。
*   **代码健壮性**: 消除了配置中的硬编码值，使代码更加清晰和易于维护。

**开发提示**:
*   为了确保后端服务能被Vite代理正确访问，请使用以下命令启动后端（请将`<YOUR_WSL_IP>`替换为你的真实WSL2 IP地址）：
    ```bash
    DJANGO_ALLOWED_HOSTS=127.0.0.1,localhost,<YOUR_WSL_IP> python manage.py runserver 0.0.0.0:8000
    ```

---

**V0.20.2 fix(fullstack): 修复管理员后台头像显示及日志删除状态同步BUG**

日期：2025-07-16

**类型**: `fix`, `ui`, `refactor`
**范围**: `frontend`, `backend`

**说明**:
本次更新集中修复了管理员后台的两个核心UI问题：一是部分场景下用户头像无法正常显示；二是访问日志在删除后，重新进入页面时会再次出现。通过重构前端URL处理逻辑、优化WebSocket状态管理以及修复后端数据序列化问题，彻底解决了上述BUG，提升了后台管理系统的稳定性和数据一致性。

**核心问题**:
1.  **头像URL硬编码**: 前端组件中硬编码了后端的开发环境地址(`http://127.0.0.1:8000`)，导致在生产环境或不同IP下，头像链接失效。
2.  **头像URL路径不一致**: 后端在通过不同方式（API、WebSocket）返回头像路径时，格式不统一（部分为相对路径，部分为完整URL），导致前端URL拼接函数出错。
3.  **WebSocket数据缺失**: 后端在通过WebSocket推送访问日志时，并未在序列化的数据中包含用户的头像URL(`user_avatar`)字段，导致前端无法渲染头像。
4.  **状态管理不同步**: 删除访问日志时，操作仅更新了组件的局部状态，而没有同步更新由`WebSocketContext`管理的全局状态，导致页面重新渲染时从全局状态中获取了旧的、未被删除的数据。

**变更内容**:

*   **1. 新增前端全局配置 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/utils/config.ts` (新增)
    *   **变更**: 创建了一个新的配置文件，用于集中管理后端的`API_BASE_URL`。该配置能自动区分开发环境和生产环境，为URL的动态构建提供了统一、可靠的基础。

*   **2. 增强URL处理函数 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/utils/config.ts`
    *   **变更**: 重构了`getFullMediaUrl`辅助函数，使其能够智能判断传入的路径是相对路径还是完整的URL。现在，无论后端返回何种格式的路径，该函数都能正确生成可用的图片地址。

*   **3. 修复后端日志序列化 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   修改了`log_to_dict`和`sync_log_to_dict`两个函数。
        *   在返回给前端的日志字典中，明确地加入了`user_avatar`字段，并从关联的`Profile`模型中获取其URL。

*   **4. 优化前端状态管理 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/WebSocketContext.tsx`
    *   **变更**:
        *   在`WebSocketContext`中增加了一个`removeLogById`函数，并将其暴露给子组件。
        *   此函数允许子组件直接操作全局的`logs`状态列表。

*   **5. 更新前端UI组件 (`ui`, `fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/AccessLogManagement.tsx`, `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**:
        *   所有需要显示头像的地方（如管理员右上角头像、访问日志列表头像），都统一使用重构后的`getFullMediaUrl`函数来获取图片`src`。
        *   在`AccessLogManagement`组件中，删除日志的`handleDelete`和`handleBatchDelete`函数现在会调用从Context获取的`removeLogById`方法，以确保全局状态同步更新。

**改进效果**:
*   **数据一致性**: 彻底解决了删除日志后数据“复活”的问题，保证了前端状态的实时同步。
*   **显示正确性**: 无论在何种环境下，管理员后台的所有用户头像现在都能稳定、正确地显示。
*   **代码健壮性**: 通过移除硬编码和增强URL处理逻辑，前端代码对环境变化的适应性更强。
*   **可维护性**: 将后端URL集中到单一配置文件中，便于未来的统一修改和维护。

---

**V0.20.1 fix(backend): 修复因代码回退导致的数据库模型不一致问题**

日期：2025-07-16

**类型**: `fix`
**范围**: `backend`

**说明**:
本次更新修复了因 Git 代码版本回退，导致数据库结构与 Django 模型定义不一致的关键 BUG。该 BUG 会在 ESP32 设备上报未注册的 NFC 卡时，因无法写入 `failure_reason` 字段而引发后端 `NOT NULL constraint failed` 错误，从而导致 WebSocket 通信中断。

**核心问题**:
1.  **模型与数据库不同步**: 代码回退到了一个没有 `failure_reason` 字段的旧版本，但数据库中依然保留着该字段且设置为非空，导致 ORM 操作失败。
2.  **通信中断**: 后端在处理 NFC 事件时因数据库写入失败而崩溃，并向 ESP32 返回一个通用的 `Internal server error`，导致设备端无法正确识别错误原因。

**变更内容**:

*   **1. 同步数据模型 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/models.py`
    *   **变更**: 在 `AccessLog` 模型中重新添加了 `failure_reason` 字段，使其与现有的数据库表结构保持一致。

*   **2. 完善日志记录逻辑 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   修改了 `handle_nfc_scan` 函数的逻辑。当检测到未注册的 NFC 卡时，会在创建 `AccessLog` 记录时，明确地将 `failure_reason` 字段设置为 `"unknown_card"`。
        *   优化了 `create_access_log` 函数，使其能够接收并处理 `failure_reason` 参数。

*   **3. 统一错误响应协议 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   将 `send_error` 函数发送的消息格式从 `{"type": "error"}` 统一为 `{"event": "error"}`，与设备端的期望协议保持一致。
        *   当 NFC 卡未知时，向设备发送的事件从通用的 `error` 明确为 `access_denied`，并附带 `reason: 'unknown_card'`，使设备端能更精确地处理拒绝事件。

**改进效果**:
*   **系统稳定性**: 彻底解决了因数据库与模型不一致导致的后端崩溃问题，确保了 WebSocket 服务的稳定运行。
*   **通信健壮性**: 统一并优化了错误响应协议，使得硬件设备能够清晰地了解访问失败的具体原因，为后续更精细化的设备端交互（如显示不同错误提示）打下了基础。
*   **数据完整性**: 保证了每一次失败的访问尝试都能被成功、完整地记录到数据库中，并附带明确的失败原因。

---

**V0.20.0 feat(frontend): 重构用户设置并统一控制台UI**

日期：2025-07-16

**类型**: `feat`, `ui`, `refactor`
**范围**: `frontend`

**说明**:
本次更新对前端的用户控制台进行了大规模的重构和界面优化，核心目标是使其在功能、布局和视觉风格上与管理员控制台保持高度一致。我们合并了原先分散的“账户信息”和“安全设置”页面，打造了一个统一的“个人设置”中心，并对两个控制台的侧边栏、标题等通用组件进行了样式统一和动画优化。

**核心问题**:
1.  **功能分散**: 用户的个人信息修改和密码修改功能被分割在两个独立的页面中，操作流程不连贯。
2.  **UI不一致**: 用户控制台与管理员控制台在侧边栏宽度、标题样式、页面布局等方面存在明显差异，影响了整体体验的统一性。
3.  **交互生硬**: 侧边栏在展开和收起时，标题的文字与图标之间是瞬间切换，缺少平滑的过渡动画。

**变更内容**:

*   **1. 重构用户“个人设置”页面 (`feat`, `refactor`, `frontend`)**:
    *   **文件**: `frontend/src/components/AccountSettings.tsx`, `frontend/src/pages/UserDashboard.tsx`, `frontend/src/App.tsx`
    *   **变更**:
        *   将原 `SecuritySettings.tsx` 组件中的密码修改功能，完全整合进 `AccountSettings.tsx` 组件中。
        *   在新的“个人设置”页面中，模仿管理员页面的布局，使用 `Divider` 组件将页面清晰地划分为“头像管理”、“基本信息”和“密码管理”三个板块。
        *   为密码管理部分增加了 `Checkbox` 以控制表单的显示/隐藏，并将新密码和确认密码输入框改为并排布局。
        *   引入了与管理员页面相同的密码强度实时检查功能和文字说明。
        *   从用户控制台的侧边栏菜单和前端路由中，彻底移除了旧的“安全设置”入口和路由，完成了代码的清理和简化。

*   **2. 统一用户与管理员控制台UI (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/UserDashboard.tsx`, `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**:
        *   **侧边栏标题**: 统一了两个控制台侧边栏顶部的标题样式。展开时均显示为“智能门禁管理系统”，收起时则显示为一个统一的皇冠图标。
        *   **侧边栏宽度**: 将用户控制台的侧边栏宽度从默认值调整为 `250px`，与管理员控制台保持一致。
        *   **顶部导航栏**: 为用户控制台的顶部导航栏增加了“用户控制台”的固定标题，使其结构与管理员页面对齐。

*   **3. 优化侧边栏过渡动画 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/UserDashboard.tsx`, `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**: 通过应用 CSS 的 `opacity` 和 `transition` 属性，优化了侧边栏在展开和收起时，标题从文字到图标的切换过程，使其变为平滑的淡入淡出动画，提升了视觉效果的流畅度。

**改进效果**:
*   **体验一致性**: 用户和管理员控制台在核心设置页面和主要框架上达到了视觉和交互的高度统一。
*   **操作流程优化**: 用户现在可以在一个页面内完成所有个人设置，操作路径更短，逻辑更清晰。
*   **界面现代化**: 通过对动画和布局的微调，提升了界面的专业度和现代感。

---

**V0.19.0 feat(esp32): 实现断电记忆并重构事件处理**

日期：2025-07-16

**类型**: `feat`, `fix`, `refactor`
**范围**: `esp32`

**说明**:
本次更新对ESP32设备端代码进行了一次全面的功能增强和代码重构。核心是引入了基于非易失性存储（NVS）的“断电记忆”功能，解决了设备重启后重复注册的问题。同时，我们修复了多个事件处理逻辑的BUG，并将所有协议事件统一到配置文件中进行管理，极大地提升了代码的健壮性和可维护性。

**核心问题**:
1.  **重复注册**: 设备在每次重启后都会重新向服务器发送注册请求，增加了不必要的网络开销和服务器负载。
2.  **未知事件**: 设备无法识别来自服务器的`access_granted`和`device_register_ack`事件，导致关键流程中断。
3.  **代码耦合**: 事件字符串硬编码在主逻辑中，不利于后期维护和扩展。

**变更内容**:

*   **1. 实现断电记忆功能 (`feat`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/access_control_device.ino`
    *   **变更**:
        *   引入了ESP32的`Preferences`库，用于操作非易失性存储（NVS）。
        *   在设备启动时，从NVS中读取`isRegistered`标志位，以判断设备是否已经注册过。
        *   当设备收到服务器的`device_register_ack`（注册成功确认）消息后，将`isRegistered`标志位写入NVS。
        *   修改了WebSocket连接逻辑，现在只有在`isRegistered`为`false`时，才会在连接成功后发送注册信息，否则仅视为一次普通的重连。

*   **2. 修复事件处理逻辑 (`fix`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/access_control_device.ino`
    *   **变更**: 在WebSocket消息处理函数`handleWebSocketMessage`中，增加了对`access_granted`, `access_denied`, 和 `device_register_ack`事件的正确处理逻辑，确保设备能响应服务器的关键指令。

*   **3. 重构事件定义 (`refactor`, `esp32`)**:
    *   **文件**: `esp32_arduino/config_template.h`, `esp32_arduino/access_control_device/config.h`, `esp32_arduino/access_control_device/access_control_device.ino`
    *   **变更**:
        *   在配置文件中新增了“协议事件定义”部分，将所有设备与服务器之间通信的事件（如`nfc_scan`, `heartbeat`等）用宏定义进行统一管理。
        *   在主程序中，将所有硬编码的事件字符串替换为新定义的宏，实现了代码与配置的分离。

**改进效果**:
*   **设备智能化**: 设备现在拥有了“记忆”，能够区分首次注册和重启重连，通信行为更加高效和专业。
*   **系统健壮性**: 修复了多个通信逻辑BUG，确保了设备与服务器之间指令交互的可靠性。
*   **代码质量**: 通过重构，代码的可读性、可维护性和可扩展性都得到了显著提升，为未来的功能迭代打下了坚实的基础。

---

**V0.18.1 fix(admin): 修复日志重复及删除交互问题**

日期：2025-07-15

**类型**: `fix`, `ui`
**范围**: `frontend`

**说明**:
本次更新主要修复了管理员“访问日志”页面中存在的两个关键BUG：一是设备单次触发（如NFC刷卡）时，前端界面会显示多条重复的日志记录；二是该问题导致的在删除单条记录时，表格勾选行为异常。同时，根据操作便利性，重新加入了单条记录的删除功能。

**核心问题**:
1.  **日志重复**: WebSocket客户端在接收到`new_access_log`消息时，没有进行去重检查，导致网络或后端服务可能重复推送的消息在界面上被多次渲染。
2.  **勾选异常**: 由于重复的日志记录拥有相同的`id`，而`id`被用作表格行的`key`，导致在对其中一条记录进行操作（如勾选、删除）时，所有具有相同`key`的行都会受到影响。
3.  **操作不便**: 在之前的版本中，移除了单行删除按钮，使得删除单条记录的操作不够直观。

**变更内容**:

*   **1. 增加WebSocket日志去重逻辑 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/WebSocketContext.tsx`
    *   **变更**: 修改了`WebSocketProvider`中处理`new_access_log`消息的逻辑。在将新日志添加到状态列表前，增加了一个检查，判断具有相同`id`的日志是否已经存在。如果存在，则忽略该条重复消息，从根源上解决了日志重复显示的问题。

*   **2. 恢复单条删除功能并优化UI (`ui`, `fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/AccessLogManagement.tsx`
    *   **变更**:
        *   在访问日志表格中重新添加了“操作”列，为每一行记录提供了独立的“删除”按钮，并使用`Popconfirm`组件来防止误操作。
        *   修正了表格的固定高度`scroll.y`的值为`600`，使其具有更好的可读性。

**改进效果**:
*   **数据准确性**: 前端界面现在能准确地反映真实的访问事件，避免了重复数据对管理员造成的困扰。
*   **交互稳定性**: 解决了因`key`值重复导致的勾选和删除异常问题，确保了表格操作的稳定性和可预测性。
*   **用户体验**: 恢复了单条删除功能，优化了日志管理的日常操作流程，使用户体验更加流畅。

---

**V0.18.0 feat(admin): 实现访问日志的删除与批量删除功能**

日期：2025-07-15

**类型**: `feat`, `ui`, `refactor`
**范围**: `frontend`, `backend`, `api`

**说明**:
本次更新为管理员仪表盘引入了核心的访问日志管理功能，允许管理员删除单条或批量删除访问记录。为实现此功能，我们创建了新的后端API端点，并对前端界面进行了相应的调整和功能开发。同时，我们还对后端的URL路由结构进行了重构，以解决API的404 Not Found问题，并优化了表格的UI展示。

**核心问题**:
1.  **功能缺失**: 管理员此前无法删除访问日志，导致日志数据只能不断累积。
2.  **UI/UX不佳**: “访问记录”和“设备列表”在数据量大时会导致页面出现滚动条，体验不佳；功能按钮布局分散。
3.  **API路由错误**: 在开发过程中，由于URL路由结构不清晰，导致前端调用新的API时出现`404 Not Found`错误。

**变更内容**:

*   **1. 新增日志删除API (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/views.py`, `serializers.py`, `urls.py`
    *   **变更**: 在`access_control`应用中，创建了处理日志删除的`AccessLogDestroyAPI`（单条删除）和`AccessLogBatchDeleteAPI`（批量删除）视图，并配置了相应的序列化器和URL路由。

*   **2. 重构后端URL结构 (`refactor`, `backend`)**:
    *   **文件**: `backend/config/urls.py`, `backend/accounts/urls.py`, `backend/face_db/urls.py`
    *   **变更**: 调整了整个项目的URL路由，将所有API端点统一归属到`/api/`前缀下。此举解决了`404`问题，并使项目路由结构更加清晰和可维护。

*   **3. 实现前端删除功能 (`feat`, `ui`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/AccessLogManagement.tsx`
    *   **变更**:
        *   为访问日志表格增加了复选框（行选择）功能。
        *   移除了原先在每行中的独立删除按钮。
        *   在顶部的筛选/操作栏中，添加了“批量删除”按钮，并集成了二次确认（`Popconfirm`）功能。
        *   使用`axios`将前端的删除操作与新创建的后端API进行了对接。

*   **4. 优化表格UI (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/AccessLogManagement.tsx`, `frontend/src/components/admin/DeviceManagement.tsx`
    *   **变更**: 为“访问记录”和“设备管理”页面的表格设置了固定的`scroll.y`高度，实现了内容区的内部滚动，优化了长列表的浏览体验。

**改进效果**:
*   **功能完整性**: 管理员现在具备了管理访问日志的能力，提升了系统的可维护性。
*   **用户体验**: 统一了功能按钮的布局，优化了表格的滚动方式，使界面更加专业和易用。
*   **系统健壮性**: 后端URL结构经过重构后更加清晰、可扩展，从根本上避免了潜在的路由冲突问题。

---

**V0.17.2 fix(websocket): 修复ESP32与后端的NFC事件通信协议**

日期：2025-07-15

**类型**: `fix`, `chore`
**范围**: `backend`, `esp32`

**说明**:
本次更新修复了ESP32设备上报NFC刷卡事件时，与后端服务器之间的通信协议不匹配问题。此前，由于数据格式的差异，后端无法正确解析来自ESP32的NFC事件，导致门禁流程中断。此修复打通了从硬件刷卡到后端接收的关键链路。

**核心问题**:
1.  **协议不匹配**: ESP32发送的JSON消息中，事件名称(`nfc_detected`)和卡号字段名(`card_uid`)与后端期望(`nfc_scan`, `card_id`)不一致。
2.  **解析逻辑错误**: 后端`DeviceConsumer`错误地期望一个嵌套的`data`字段，而ESP32发送的是扁平的JSON结构。
3.  **配置模板过时**: `esp32_arduino/config_template.h`文件中的配置项（特别是NFC引脚）已过时，与当前可正常工作的`config.h`不一致。

**变更内容**:

*   **1. 修复ESP32事件格式 (`fix`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/access_control_device.ino`
    *   **变更**: 在`sendNFCEvent`函数中，将发送的JSON负载的`event`字段修改为`"nfc_scan"`，`card_uid`字段修改为`"card_id"`，以匹配后端协议。

*   **2. 修复后端解析逻辑 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**: 修改了`DeviceConsumer`的`receive`方法，使其能够正确处理扁平化的JSON消息结构，直接从顶层解析`card_id`等数据，而不是从一个不存在的`data`子对象中查找。

*   **3. 同步配置模板 (`chore`, `esp32`)**:
    *   **文件**: `esp32_arduino/config_template.h`
    *   **变更**: 根据当前可用的`config.h`更新了模板文件，同步了所有配置项（特别是NFC引脚），并保留了`WIFI`和服务器IP的占位符，确保了模板的准确性和通用性。
    *   **文件**: `esp32_arduino/access_control_device/config.h`
    *   **变更**: 将此文件添加到git跟踪中，以便于版本控制。

**改进效果**:
*   **链路打通**: 成功解决了从硬件到后端的NFC事件上报问题，现在刷卡操作可以被后端正确识别和处理。
*   **代码一致性**: 确保了前后端及硬件之间通信协议的一致性。
*   **可维护性提升**: 更新了配置模板，方便未来新设备的配置和项目维护。

---

**V0.17.1 docs(guide): 添加WSL2与ESP32网络连接配置指南**

日期：2025-07-14

**类型**: `docs`
**范围**: `documentation`, `wsl`

**说明**:
本次更新为项目添加了一份关键的技术文档，详细说明了如何在WSL2这种特殊的开发环境下，配置网络以允许ESP32等外部物理设备连接到本地运行的后端服务。

**核心问题**:
*   项目之前缺少关于在WSL2环境中进行网络配置的官方说明，这对于新加入的开发者或未来的维护工作来说是一个潜在的障碍和时间成本。

**变更内容**:

*   **1. 新增网络配置文档 (`docs`, `documentation`)**:
    *   **文件**: `docs/WSL2_ESP32_NETWORK_SETUP.md`
    *   **变更**: 创建了一个新的Markdown文件，详细记录了通过Windows端口转发功能，允许局域网内物理设备（如ESP32）访问WSL2内部服务的完整步骤。内容涵盖：
        *   获取WSL2动态IP地址的方法。
        *   使用`netsh`命令设置和管理端口转发规则。
        *   配置Windows防火墙入站规则。
        *   修改ESP32固件中的服务器IP指向。
        *   总结了日常开发的标准化流程。

**改进效果**:
*   **知识沉淀**: 将调试过程中积累的关键网络配置经验固化为项目文档，方便团队成员查阅和复现，避免了重复性的调试工作。
*   **降低上手门槛**: 为新成员或需要在类似环境中进行开发的工程师提供了清晰、可操作的设置指南，显著节省了环境配置的时间。

---

**V0.17.0 feat(dashboard): 实现管理员仪表盘全栈实时监控功能**

日期：2025-07-14

**类型**: `feat`
**范围**: `backend`, `frontend`, `websocket`

**说明**:
本次更新是一次重大的全栈功能开发，为管理员仪表盘引入了基于WebSocket的实时监控能力。现在，“设备管理”和“访问日志”两个模块能够实时地、自动地反映系统的最新状态，无需手动刷新。此更新极大地提升了系统的可用性和管理员的操作体验。

**核心问题**:
*   **缺乏实时性**: 管理员无法实时看到设备的上下线状态和新发生的访问事件，必须手动刷新页面来获取旧数据。
*   **数据丢失**: 刷新浏览器页面会导致当前已加载的设备和日志信息全部丢失，用户体验差（“冷启动”问题）。
*   **架构落后**: 前端采用的是效率低下的定时轮询（Polling）方式来获取数据。

**变更内容**:

*   **1. 后端数据模型扩展 (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/models.py`, `backend/access_control/migrations/0002_device_accesslog_device.py`
    *   **变更**:
        *   新增了 `Device` 模型，用于在数据库中追踪和存储每个硬件设备的状态（在线/离线）、IP地址、最后上线时间等关键信息。
        *   在 `AccessLog` 模型中添加了到 `Device` 模型的外键关联，使得每一条访问记录都能明确归属于特定的设备。
        *   生成了新的数据库迁移文件以应用上述模型变更。

*   **2. 实时WebSocket后端架构 (`feat`, `refactor`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   **增强 `DeviceConsumer`**: 在设备连接(`connect`)和断开(`disconnect`)时，会立刻更新数据库中对应设备的`status`字段，并向管理员广播状态变更事件。
        *   **重构 `AdminMonitorConsumer`**:
            *   **解决冷启动**: 当管理员前端连接时，消费者会立刻从数据库查询所有设备和近期日志，并将这份“初始状态快照”主动推送给前端。
            *   **实时转发**: 监听由`DeviceConsumer`广播的设备状态更新和新访问日志事件，并实时地将这些事件转发给所有连接的管理员客户端。

*   **3. 前端实时架构重构 (`feat`, `refactor`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/WebSocketContext.tsx`, `frontend/src/pages/AdminDashboard.tsx`
    *   **变更**:
        *   创建了全新的 `WebSocketProvider` 和 `useWebSocket` hook，构建了一套基于React Context的客户端WebSocket状态管理中心。
        *   该Context统一处理WebSocket连接、断线重连、消息接收和状态管理（设备列表和日志列表），为所有子组件提供单一、可靠的实时数据源。
        *   重构了 `AdminDashboard` 页面，使用 `WebSocketProvider` 包裹整个仪表盘，实现了实时数据在全局的注入。

*   **4. 仪表盘组件实时化改造 (`feat`, `ui`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/DeviceManagement.tsx`, `frontend/src/components/admin/AccessLogManagement.tsx`
    *   **变更**:
        *   彻底移除了组件内部原有的、基于`useEffect`和`setInterval`的定时轮询逻辑。
        *   改造组件，使其直接通过`useWebSocket` hook消费来自Context的`devices`和`logs`实时数据。
        *   修复了`AccessLogManagement`中因状态派生逻辑复杂导致的日志不显示问题，改为使用`React.useMemo`进行即时计算，确保了数据渲染的可靠性。

**改进效果**:
*   **核心功能实现**: 成功交付了管理员仪表盘的实时监控功能，这是本项目的一个重要里程碑。
*   **卓越的实时性**: 管理员现在可以毫秒级地看到设备的上下线状态变化和新发生的访问日志，无需任何手动操作。
*   **优秀的用户体验**: 彻底解决了刷新页面导致数据丢失的问题，无论何时进入或刷新页面，都能看到系统的完整、最新状态。
*   **先进的架构**: 前端架构从落后的定时轮询升级为现代的、高效的WebSocket实时数据流，后端也构建了清晰、可扩展的消费者广播模型。

---

**V0.16.1 fix(websocket): 修复设备与后端的双向通信协议并增强稳定性**

日期：2025-07-14

**类型**: `fix`
**范围**: `backend`, `esp32`

**说明**:
本次更新是一次关键的问题修复，彻底解决了ESP32设备与后端服务器之间WebSocket通信不稳定、导致设备反复重启的严重问题。通过对双向通信协议的梳理和统一，以及对设备端和后端代码逻辑的加固，确保了设备能够建立并维持长期稳定的连接。

**核心问题**:
*   **设备崩溃重启**: ESP32在收到后端发送的`connection_established`消息后，因协议字段不匹配(`type` vs `event`)导致解析时出现空指针，引发设备崩溃并无限重启。
*   **后端异常断连**: 后端在处理完设备的`device_register`事件后，消费者(Consumer)进程意外退出，导致WebSocket连接被异常关闭（错误码 1006）。
*   **心跳时间错误**: 后端在响应设备心跳时，未能返回真实的服务器时间。

**变更内容**:

*   **1. WebSocket通信协议统一 (`fix`, `backend`, `esp32`)**:
    *   **文件**: `backend/access_control/consumers.py`, `esp32_arduino/access_control_device/access_control_device.ino`
    *   **变更**: 将所有客户端与服务端之间的JSON消息关键字段从`type`彻底统一为`event`，解决了因协议不一致导致的一系列解析错误。

*   **2. 后端消费者逻辑修复 (`fix`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`
    *   **变更**:
        *   在处理`device_register`事件后，增加了一条`device_register_ack`确认消息的回送，确保了交互的完整性，防止了消费者进程的意外终止。
        *   修正了`handle_heartbeat`方法，使其能够正确获取并返回`timezone.now()`作为真实的服务器时间。

*   **3. ESP32代码健壮性增强 (`fix`, `esp32`)**:
    *   **文件**: `esp32_arduino/access_control_device/access_control_device.ino`
    *   **变更**: 在消息处理函数`handleWebSocketMessage`中增加了对`event`字段的空指针检查，极大地提高了代码的容错能力，避免了因收到未知格式消息而导致的设备崩溃。

*   **4. 设备认证密钥格式修正 (`fix`, `esp32`)**:
    *   **文件**: `esp32_arduino/config_template.h`
    *   **变更**: (此为调试过程中的关键修复) 更新了`DEVICE_KEY`的格式，使其与后端认证中间件期望的`device_{device_id}_secret_key`格式完全匹配。

**改进效果**:
*   **通信稳定性**: 彻底解决了设备与后端之间的连接和通信问题，设备现在可以长期稳定在线，不再出现断连或重启。
*   **协议一致性**: 统一了双向WebSocket通信协议，为后续功能的开发奠定了坚实的基础，降低了未来出错的风险。
*   **代码健壮性**: 显著增强了设备端和后端代码的容错能力和稳定性。

---

**V0.16.0.1 chore(backend): 调整模型存储路径并新增项目审查报告**

日期：2025-07-14

**类型**: `chore`, `docs`
**范围**: `backend`, `docs`

**说明**:
本次更新主要对项目的配置和文档进行了完善。首先，修改了Django的配置，将DeepFace人脸识别模型的默认下载路径从用户主目录重定向到项目仓库内部的`backend/model/`目录下，便于模型的统一管理和版本控制。其次，更新了`.gitignore`文件以忽略该模型目录，避免将庞大的模型文件提交到版本库。最后，新增了一份详细的项目审查报告，对当前代码库的整体情况进行了分析和总结。

**变更内容**:

*   **1. DeepFace模型路径配置 (`chore`, `backend`)**:
    *   **文件**: `backend/config/settings.py`
    *   **变更**: 通过设置`DEEPFACE_HOME`环境变量，将`deepface`库的模型下载和存储路径指定为项目内的`backend/model/`目录。

*   **2. Git忽略规则更新 (`chore`, `backend`)**:
    *   **文件**: `backend/.gitignore`
    *   **变更**: 在忽略列表中添加了`model/`条目，以确保下载的人脸识别模型不会被Git追踪。

*   **3. 新增项目审查报告 (`docs`, `docs`)**:
    *   **文件**: `docs/REVIEW_REPORT.md`
    *   **内容**: 添加了一份完整的项目审查报告，内容包括项目概述、文档评估、前后端实现状态分析以及下一步的开发建议。

**改进效果**:
*   **项目可移植性**: 模型作为项目的一部分进行管理（但不纳入版本控制），简化了新环境的部署流程。
*   **仓库整洁性**: 避免了将大型二进制模型文件意外提交到Git仓库。
*   **文档完善**: 提供了清晰的项目现状分析，为后续开发提供了明确的参考依据。

---

**V0.16.0 feat(hardware): 新增ESP32 Arduino代码实现硬件设备集成**

日期：2025-07-14

**类型**: `feat`
**范围**: `hardware`

**说明**:
本次更新新增了ESP32设备端的Arduino代码，实现了智能门禁系统的硬件集成功能，包括WiFi连接、WebSocket通信、NFC读卡、门禁控制等核心硬件功能，为课程设计的硬件演示提供完整的设备端支持。

**新增功能**:

*   **1. ESP32主控代码 (`feat`, `hardware`)**:
    *   **文件**: `esp32_arduino/access_control_device/access_control_device.ino`
    *   **功能实现**:
        *   WiFi自动连接和重连机制，支持网络状态监控
        *   WebSocket客户端实现，与服务器建立实时双向通信
        *   设备注册和身份验证，使用设备密钥进行安全认证
        *   心跳包机制，每30秒发送设备状态保持连接
        *   NFC读卡功能，支持ISO14443A标准卡片读取
        *   门禁控制系统，继电器控制门锁开关
        *   手动开门按钮，支持紧急手动操作
        *   LED状态指示，绿灯表示在线，红灯表示离线/错误
        *   蜂鸣器声音提示，不同操作对应不同提示音
        *   防重复读卡机制，避免同一张卡连续触发

*   **2. 硬件接口定义 (`feat`, `hardware`)**:
    *   **引脚配置**:
        *   MFRC522 NFC模块：RST(GPIO22), SS(GPIO21)
        *   输出设备：蜂鸣器(GPIO18), 绿LED(GPIO19), 红LED(GPIO23), 继电器(GPIO25)
        *   输入设备：手动按钮(GPIO26)
        *   SPI通信：支持NFC模块高速数据传输

*   **3. 通信协议实现 (`feat`, `hardware`)**:
    *   **WebSocket消息格式**:
        *   设备注册：包含设备ID、名称、密钥、IP地址、固件版本
        *   NFC事件：卡片UID、设备ID、时间戳
        *   心跳包：设备状态、门锁状态、在线时间
        *   门禁状态：开门/关门状态变化通知
        *   设备状态：WiFi信号强度、内存使用、运行时间
    *   **服务器命令支持**:
        *   远程门禁控制：开门/关门命令
        *   设备重启：远程重启设备
        *   状态查询：实时获取设备状态

*   **4. 项目文档 (`docs`, `hardware`)**:
    *   **文件**: `esp32_arduino/README.md`
    *   **文档内容**:
        *   详细的硬件连接图和引脚说明
        *   Arduino库依赖和安装步骤
        *   WiFi和服务器配置说明
        *   通信协议和消息格式文档
        *   功能特性和使用说明
        *   故障排除和调试技巧
        *   版本信息和技术支持

**技术特性**:
*   **实时通信**: WebSocket协议确保设备与服务器实时双向通信
*   **自动重连**: WiFi和WebSocket连接断开时自动重连机制
*   **安全认证**: 设备密钥验证确保只有授权设备可以连接
*   **状态监控**: 实时监控设备在线状态、门锁状态、网络状态
*   **硬件控制**: 完整的门禁硬件控制，包括NFC、继电器、指示灯、蜂鸣器
*   **防抖处理**: 按钮防抖和NFC防重复读取机制
*   **错误处理**: 完善的错误处理和状态指示机制

**改进效果**:
*   **硬件集成**: 实现了完整的ESP32硬件设备端功能，支持真实的门禁操作
*   **网络发现**: 设备连接到局域网后可被网页端检测和管理
*   **实时交互**: 支持NFC刷卡、远程开门、状态监控等实时交互功能
*   **课程演示**: 为课程设计提供了完整的硬件演示平台
*   **扩展性**: 预留了摄像头、门磁传感器等扩展硬件接口

---

**V0.15.1 fix(frontend): 修复管理员页面TypeScript编译错误和类型问题**

日期：2025-07-14

**类型**: `fix`
**范围**: `frontend`

**说明**:
本次更新修复了新增的管理员页面组件中的TypeScript编译错误，包括未使用的导入、类型不匹配和参数未使用等问题，确保前端代码能够正常编译构建。

**修复内容**:

*   **1. 访问日志管理页面编译错误修复 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/AccessLogManagement.tsx`
    *   **修复问题**:
        *   移除未使用的导入：`axios`, `Divider`, `SearchOutlined`, `DownloadOutlined`, `FilterOutlined`
        *   修复RangePicker的onChange类型不匹配问题，添加`handleDateRangeChange`函数处理日期范围变化
        *   暂时注释axios导入，等待真实API实现

*   **2. 设备管理页面编译错误修复 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/DeviceManagement.tsx`
    *   **修复问题**:
        *   移除未使用的导入：`axios`, `Divider`, `WifiOutlined`, `DisconnectOutlined`, `DeleteOutlined`, `PoweroffOutlined`
        *   移除未使用的`Option`解构
        *   修复`handleDeviceControl`函数中未使用的`deviceId`参数，改为`_deviceId`
        *   暂时注释axios导入，等待真实API实现

*   **3. 系统设置页面编译错误修复 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/SystemSettings.tsx`
    *   **修复问题**:
        *   移除未使用的导入：`Tag`, `Tooltip`, `ExclamationCircleOutlined`, `CheckCircleOutlined`, `InfoCircleOutlined`
        *   移除未使用的`TextArea`解构
        *   移除未使用的`loading`状态变量
        *   简化`loadSettings`函数，移除不必要的loading状态管理

**改进效果**:
*   **编译成功**: 解决了所有TypeScript编译错误，确保前端代码能够正常构建
*   **代码质量**: 移除了未使用的导入和变量，提升了代码的整洁性
*   **类型安全**: 修复了类型不匹配问题，确保TypeScript类型检查通过
*   **开发体验**: 消除了编译警告，提升了开发调试体验

---

**V0.15.0 feat(admin): 完善管理员页面核心功能模块，移除模拟数据等待真实设备连接**

日期：2025-07-14

**类型**: `feat`
**范围**: `admin`

**说明**:
本次更新完善了管理员界面中标记为"开发中"的核心功能模块，包括设备管理、访问日志管理和系统设置页面。移除了所有模拟数据，改为等待真实的ESP32设备连接和访问记录，为课程设计的硬件集成演示做好准备。

**新增功能**:

*   **1. 设备管理页面 (`feat`, `admin`)**:
    *   **文件**: `frontend/src/components/admin/DeviceManagement.tsx`
    *   **功能实现**:
        *   完整的设备管理界面，包含设备列表、统计卡片、操作按钮
        *   设备状态监控（在线/离线/故障）、远程控制、添加/编辑设备功能
        *   移除模拟ESP32设备数据，预留真实API接口调用
        *   添加友好提示信息，当无设备时显示"暂无设备连接"

*   **2. 访问日志管理页面 (`feat`, `admin`)**:
    *   **文件**: `frontend/src/components/admin/AccessLogManagement.tsx`
    *   **功能实现**:
        *   完整的访问记录管理界面，包含日志列表、统计数据、筛选功能
        *   支持时间范围筛选、状态过滤、用户搜索、数据导出等功能
        *   移除模拟访问记录数据，预留真实API接口调用
        *   添加友好提示信息，当无记录时显示"暂无访问记录"

*   **3. 系统设置页面 (`feat`, `admin`)**:
    *   **文件**: `frontend/src/components/admin/SystemSettings.tsx`
    *   **功能实现**:
        *   完整的系统配置界面，分为门禁设置、安全设置、通知设置、基础设置四大模块
        *   包含人脸识别阈值、访问超时、密码策略、通知配置等实用设置项
        *   提供用户友好的配置界面，支持实时保存和重置功能

*   **4. 路由集成和依赖管理 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/App.tsx`, `frontend/package.json`
    *   **功能实现**:
        *   将三个新页面完整集成到管理员仪表盘路由中
        *   为每个页面添加ErrorBoundary错误边界保护
        *   添加dayjs依赖支持时间处理功能
        *   移除所有"开发中"的占位符提示

**改进效果**:
*   **功能完整性**: 管理员界面现已完全消除"开发中"提示，所有核心功能页面均已实现
*   **用户体验**: 提供了专业的管理界面，支持设备监控、日志分析、系统配置等完整功能
*   **扩展性**: 预留了真实API接口，便于后续与ESP32硬件设备集成

---

**V0.14.1 feat(chore): 添加.gitignore规划，停止追踪根目录下的debug.log文件**

日期：2025-07-14

**类型**: `feat`
**范围**: `chore`

**说明**:
本次更新优化了项目的版本控制配置，通过添加.gitignore规则来停止追踪根目录下的debug.log文件。这一改动有助于保持代码仓库的整洁，避免将调试日志文件意外提交到版本控制系统中，提升了项目的维护效率和代码仓库的质量。

**新增功能**:

*   **1. .gitignore配置优化 (`chore`, `project`)**:
    *   **文件**: `.gitignore`
    *   **新增忽略规则**:
        *   `debug.log` - 停止追踪根目录下的调试日志文件
        *   防止调试信息意外提交到代码仓库
        *   保持项目根目录的整洁性

**改进效果**:
*   **性能优化**: 通过减少不必要的文件追踪，提升了Git的性能，减少了版本控制的开销。
*   **安全性提升**: 通过避免敏感调试信息泄露到版本历史中，提升了项目的安全性。
*   **维护效率提升**: 通过统一团队开发环境的文件管理规范，提升了项目的维护效率。

---

**V0.14.0 feat(admin): 新增管理员个人设置页面和完整账户管理功能**

日期：2025-07-11

**类型**: `feat`
**范围**: `admin`

**说明**:
本次更新为管理员仪表盘新增了专门的个人设置页面，实现了管理员自身账户的完整管理功能，包括用户名修改、头像上传和密码修改。通过创建独立的管理员个人设置页面，将管理员自身账户管理与用户管理功能明确分离，提供了更清晰的功能架构和更好的用户体验。

**新增功能**:

*   **1. 管理员个人设置页面 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/AdminAccountSettings.tsx`
    *   **功能完整性**:
        *   头像管理：支持JPG/PNG格式图片上传，最大2MB限制
        *   用户名修改：实时验证，3-20字符长度限制
        *   密码修改：集成密码强度检测和安全验证
        *   实时预览：头像上传前即时预览效果
    *   **用户界面设计**:
        *   分区布局：头像管理、基本信息、密码管理三个清晰分区
        *   专业标识：使用金色编辑图标突出管理员身份
        *   响应式设计：适配不同屏幕尺寸的布局
        *   一致性设计：与普通用户设置页面保持相同的交互模式

*   **2. 集成密码管理功能 (`feat`, `frontend`)**:
    *   **密码修改界面**:
        *   可选功能：通过复选框控制是否修改密码
        *   三字段验证：当前密码、新密码、确认密码
        *   密码强度检测：实时显示密码强度等级（弱/中等/强）
        *   安全提示：密码复杂度建议和安全提醒
    *   **密码强度算法**:
        *   多维度评分：长度、大小写字母、数字、特殊字符
        *   视觉反馈：不同强度等级使用不同颜色和图标
        *   实时更新：输入过程中动态显示强度变化
    *   **安全机制**:
        *   密码修改成功后强制重新登录
        *   防止新密码与当前密码相同
        *   服务端验证确保密码安全性

*   **3. 头像上传系统 (`feat`, `frontend`)**:
    *   **上传功能**:
        *   文件类型验证：仅支持JPEG和PNG格式
        *   文件大小限制：最大2MB，防止服务器负载过重
        *   即时预览：选择文件后立即显示预览效果
        *   自动上传：选择文件后自动触发上传流程
    *   **API集成**:
        *   使用FormData进行multipart/form-data上传
        *   管理员token认证确保安全性
        *   错误处理：网络异常和服务器错误的友好提示
        *   成功反馈：上传成功后的即时通知

**架构优化**:

*   **4. 路由系统重构 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/App.tsx`
    *   **新增路由**:
        *   `/admin/dashboard/account` - 管理员个人设置页面
        *   移除独立的安全设置路由，功能集成到个人设置页面
    *   **路由保护**:
        *   使用ErrorBoundary包装确保错误隔离
        *   管理员token验证确保访问安全性
    *   **嵌套路由优化**:
        *   清晰的路由层级结构
        *   与用户仪表盘路由保持一致的命名规范

*   **5. 管理员仪表盘菜单优化 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **菜单结构调整**:
        *   新增"个人设置"菜单项，使用用户图标
        *   移除独立的"安全设置"菜单项
        *   保持菜单项的逻辑分组和视觉层次
    *   **头像显示增强**:
        *   支持动态头像显示，自动处理URL前缀
        *   默认用户图标作为fallback
        *   实时更新：头像上传后立即在头部显示

**事件系统增强**:

*   **6. 管理员资料更新事件 (`feat`, `frontend`)**:
    *   **事件机制**:
        *   `adminProfileUpdated`自定义事件用于跨组件通信
        *   支持用户名和头像的实时更新
        *   事件监听器的正确注册和清理
    *   **状态同步**:
        *   个人设置页面修改后，仪表盘头部立即更新
        *   避免页面刷新，提供流畅的用户体验
        *   状态管理的一致性确保数据准确性

**用户体验设计**:

*   **7. 交互流程优化 (`ui`, `frontend`)**:
    *   **表单设计**:
        *   分离的表单实例：基本信息表单和密码表单独立管理
        *   条件显示：密码修改功能通过复选框控制显示
        *   即时验证：表单字段的实时验证和错误提示
    *   **加载状态管理**:
        *   独立的loading状态：用户名更新、头像上传、密码修改
        *   按钮状态反馈：loading期间显示相应的提示文字
        *   防重复提交：loading期间禁用相关操作
    *   **成功反馈机制**:
        *   操作成功的即时通知
        *   密码修改后的安全提醒和自动跳转
        *   头像上传的视觉确认

**API集成完善**:

*   **11. 后端API复用 (`integration`, `frontend`)**:
    *   **现有API利用**:
        *   复用`/api/auth/update-username/`用于用户名修改
        *   复用`/api/auth/profile/`用于头像上传
        *   复用`/api/auth/change-password/`用于密码修改
    *   **认证机制**:
        *   使用`adminAuthToken`确保管理员身份验证
        *   正确的Authorization头设置
        *   错误处理和用户反馈的统一模式

---

**V0.13.1 feat(admin): 管理员页面新增用户密码修改功能**

日期：2025-07-11

**类型**: `feat`
**范围**: `admin`

**说明**:
本次更新为管理员用户管理页面添加了完整的密码修改功能，包括后端API支持和前端用户界面。管理员现在可以在编辑用户信息时选择性地修改用户密码，提供了与普通用户密码管理相同的安全验证机制，增强了管理员对用户账户的完整管理能力。

**新增功能**:

*   **1. 后端密码更新API支持 (`feat`, `backend`)**:
    *   **文件**: `backend/accounts/serializers.py`
    *   **AdminUserUpdateSerializer增强**:
        *   新增可选密码字段：`password` (write_only, min_length=6, required=False)
        *   新增确认密码字段：`confirm_password` (write_only, required=False)
        *   支持空值处理：`allow_blank=True`，允许不修改密码的情况
    *   **密码验证逻辑**:
        *   条件验证：只有提供密码时才进行验证
        *   密码确认验证：确保两次输入的密码一致
        *   安全性检查：密码最少6个字符要求
    *   **更新逻辑优化**:
        *   使用`instance.set_password()`确保密码正确加密
        *   密码字段从validated_data中安全移除
        *   保持原有Profile更新逻辑不变

*   **2. 前端密码管理界面 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserEditModal.tsx`
    *   **用户界面增强**:
        *   新增"密码管理"分区，使用Divider组件分隔
        *   添加"修改用户密码"复选框控制密码修改功能
        *   条件显示密码输入字段，避免界面混乱
        *   "权限设置"分区，保持界面结构清晰
    *   **密码输入组件**:
        *   新密码输入框：带锁图标，placeholder提示
        *   确认密码输入框：依赖新密码字段进行验证
        *   自动完成属性：`autoComplete="new-password"`
        *   响应式布局：两个密码字段各占50%宽度

*   **3. 智能表单验证系统 (`feat`, `frontend`)**:
    *   **条件验证规则**:
        *   密码字段：仅在选择修改密码时为必填
        *   最小长度验证：密码至少6个字符
        *   确认密码验证：依赖新密码字段，确保一致性
    *   **动态验证逻辑**:
        *   复选框状态控制验证规则的启用/禁用
        *   实时验证反馈，提供即时错误提示
        *   自定义validator函数处理复杂验证逻辑

**用户体验优化**:

*   **4. 交互逻辑优化 (`ui`, `frontend`)**:
    *   **状态管理**:
        *   新增`changePassword`状态控制密码修改功能
        *   复选框切换时自动清除密码字段内容
        *   表单重置时同步重置密码修改状态
    *   **提交逻辑优化**:
        *   条件数据提交：不修改密码时自动移除密码字段
        *   避免发送空密码数据到后端
        *   保持原有用户信息更新逻辑完整性
    *   **界面布局改进**:
        *   使用Divider组件创建清晰的功能分区
        *   密码管理、权限设置分别独立显示
        *   保持与用户创建页面的设计一致性

**技术实现细节**:

*   **5. 后端序列化器架构 (`refactor`, `backend`)**:
    *   **字段定义优化**:
        *   密码字段设为可选：`required=False, allow_blank=True`
        *   写入专用字段：`write_only=True`确保密码不会被序列化输出
        *   长度验证：`min_length=6`确保密码安全性
    *   **验证方法增强**:
        *   `validate()`方法处理密码确认逻辑
        *   条件验证：只有提供密码时才验证确认密码
        *   错误消息清晰：提供具体的验证失败原因
    *   **更新方法安全性**:
        *   使用Django的`set_password()`方法确保密码加密
        *   安全地从validated_data中移除敏感字段
        *   保持事务完整性

*   **6. 前端组件架构 (`refactor`, `frontend`)**:
    *   **导入优化**:
        *   新增Divider、Checkbox组件导入
        *   新增LockOutlined图标导入
        *   保持代码组织清晰
    *   **状态管理**:
        *   `changePassword`布尔状态控制密码修改功能
        *   状态变化时的副作用处理（清除表单字段）
        *   组件卸载时的状态重置
    *   **表单处理逻辑**:
        *   条件字段提交：根据changePassword状态决定是否包含密码字段
        *   表单验证规则动态调整
        *   错误处理保持与原有逻辑一致

**安全性增强**:

*   **7. 密码安全处理 (`security`, `backend`)**:
    *   **加密存储**:
        *   使用Django内置的`set_password()`方法
        *   确保密码经过proper hashing处理
        *   避免明文密码存储风险
    *   **传输安全**:
        *   密码字段标记为`write_only=True`
        *   API响应中不包含密码信息
        *   前端使用`autoComplete="new-password"`属性
    *   **验证安全**:
        *   服务端验证密码长度和确认密码
        *   前端实时验证提供即时反馈
        *   防止弱密码设置

**API接口完整性**:

*   **8. 管理员API增强 (`feat`, `backend`)**:
    *   **现有API扩展**:
        *   `/api/admin/users/{id}/update/` 现在支持密码更新
        *   保持向后兼容性：不提供密码字段时不修改密码
        *   错误处理：密码验证失败时返回详细错误信息
    *   **数据处理**:
        *   智能字段处理：自动识别并处理密码相关字段
        *   Profile数据更新逻辑保持不变
        *   用户权限更新逻辑保持不变

---

**V0.13.0 feat(frontend): 完整实现登录注册状态提示系统和优化用户体验流程**

日期：2025-07-11

**类型**: `feat`
**范围**: `frontend`

**说明**:
本次更新全面优化了用户登录、注册和管理员登录的用户体验，实现了完整的状态提示系统，包括加载状态、成功/失败反馈、智能跳转流程和仪表盘欢迎信息。通过引入顶部浮动状态卡片、优化时序控制、修复Antd组件警告和防止重复显示问题，显著提升了用户操作的可视化反馈和整体使用体验。

**新增功能**:

*   **1. 顶部状态提示组件系统 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AuthPage.tsx`
    *   **智能状态卡片**:
        *   固定定位的顶部浮动提示卡片
        *   毛玻璃背景效果 (`backdrop-filter: blur(10px)`)
        *   动态边框颜色：蓝色(加载)、绿色(成功)、红色(错误)
        *   响应式设计，最小宽度300px，最大宽度500px
    *   **多状态支持**:
        *   加载状态：蓝色旋转loading图标 + "正在登录/注册，请稍候..."
        *   成功状态：绿色勾选图标 + 个性化成功信息
        *   错误状态：红色感叹号图标 + 具体错误信息
    *   **自动消失机制**:
        *   成功状态：跳转时自动清除
        *   错误状态：3秒后自动消失

*   **2. 管理员登录状态提示系统 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminLoginPage.tsx`
    *   **专业化管理员体验**:
        *   管理员身份验证提示："正在验证管理员身份，请稍候..."
        *   成功状态个性化信息："管理员登录成功！正在跳转到管理后台..."
        *   错误状态详细反馈："管理员登录失败：${具体错误信息}"
    *   **视觉设计统一**:
        *   与用户登录页面相同的状态卡片设计
        *   一致的图标和颜色方案
        *   相同的交互时序和动画效果

*   **3. 仪表盘欢迎信息系统 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/pages/UserDashboard.tsx`
    *   **用户仪表盘欢迎**:
        *   登录成功后在仪表盘显示欢迎通知
        *   个性化欢迎信息："欢迎回来，${username}！"
        *   绿色勾选图标，4秒显示时长
        *   页面加载完成后0.5秒延迟显示
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **管理员仪表盘欢迎**:
        *   管理员登录成功后的专属欢迎通知
        *   管理员专属信息："欢迎进入管理后台，${username}！"
        *   金色皇冠图标，突出管理员身份
        *   与用户仪表盘相同的显示时序

**用户体验优化**:

*   **4. 智能跳转流程优化 (`ui`, `frontend`)**:
    *   **优化跳转时序**:
        *   登录成功 → 显示成功信息 → 1.5秒后直接跳转 → 仪表盘显示欢迎
        *   注册成功 → 显示成功信息 → 2秒后直接切换到登录面板（移除切换提示）
    *   **localStorage状态传递**:
        *   登录页面存储欢迎信息到localStorage
        *   仪表盘页面读取并显示欢迎信息
        *   显示后立即清除，避免重复显示

*   **5. 按钮状态增强 (`ui`, `frontend`)**:
    *   **加载状态管理**:
        *   独立的signInLoading和signUpLoading状态
        *   按钮文字动态变化："登录中..."、"注册中..."
        *   loading期间按钮不可重复点击
        *   加载动画与状态卡片同步显示
    *   **错误恢复机制**:
        *   使用finally块确保loading状态正确重置
        *   无论成功或失败都会恢复按钮可用状态

**问题修复**:

*   **6. Antd组件兼容性修复 (`fix`, `frontend`)**:
    *   **notification组件警告修复**:
        *   问题：`Warning: [antd: notification] Static function can not consume context like dynamic theme`
        *   原因：直接使用`notification.success()`静态方法无法消费App组件的context
        *   修复：改用`App.useApp()`获取notification实例
        *   影响文件：UserDashboard.tsx, AdminDashboard.tsx
    *   **Dropdown组件警告修复**:
        *   问题：`Warning: [antd: Dropdown] overlay is deprecated. Please use menu instead`
        *   状态：已识别，需要后续修复overlay属性

*   **7. 重复显示问题修复 (`fix`, `frontend`)**:
    *   **欢迎信息重复显示修复**:
        *   问题：useEffect被触发多次导致欢迎信息显示两次
        *   原因：React严格模式或组件重新渲染触发多次useEffect
        *   修复方案：
            *   添加`welcomeShown`和`adminWelcomeShown`状态标记
            *   在显示前立即清除localStorage
            *   添加条件判断`if (welcomeMessage && !welcomeShown)`
        *   技术实现：状态管理 + 提前清除机制

*   **8. 注册流程优化 (`ui`, `frontend`)**:
    *   **移除不必要的切换提示**:
        *   问题：注册成功后显示"正在切换到登录页面..."提示冗余
        *   修复：注册成功后直接在2秒后切换到登录面板
        *   效果：用户体验更加流畅，减少等待感

**技术实现细节**:

*   **9. 状态管理架构 (`refactor`, `frontend`)**:
    *   **新增状态变量**:
        *   `statusMessage`: 状态提示文本内容
        *   `statusType`: 状态类型 ('loading' | 'success' | 'error')
        *   `welcomeShown`: 防止用户欢迎信息重复显示
        *   `adminWelcomeShown`: 防止管理员欢迎信息重复显示
    *   **localStorage临时存储优化**:
        *   存储：登录成功后存储欢迎信息
        *   读取：仪表盘页面检查并显示
        *   清除：显示前立即清除，防止重复

*   **10. 调试信息系统 (`feat`, `frontend`)**:
    *   **完整的调试日志**:
        *   登录页面：localStorage存储日志
        *   仪表盘页面：读取、显示、清除全流程日志
        *   便于开发和问题排查
    *   **调试日志示例**:
        *   `AuthPage: 存储欢迎信息: 欢迎回来，username！`
        *   `UserDashboard: 检查欢迎信息: 欢迎回来，username！`
        *   `UserDashboard: 显示欢迎通知`

**交互流程完整性**:

*   **11. 用户登录完整流程 (`feat`, `frontend`)**:
    *   点击登录 → 顶部显示"正在登录，请稍候..." → 成功后显示"登录成功！正在跳转到仪表板..." → 1.5秒后跳转 → 仪表盘显示"欢迎回来，${username}！"

*   **12. 用户注册完整流程 (`feat`, `frontend`)**:
    *   点击注册 → 顶部显示"正在注册，请稍候..." → 成功后显示"注册成功！欢迎加入智能门禁系统，${username}！" → 2秒后直接切换到登录面板

*   **13. 管理员登录完整流程 (`feat`, `frontend`)**:
    *   点击登录 → 顶部显示"正在验证管理员身份，请稍候..." → 成功后显示"管理员登录成功！正在跳转到管理后台..." → 1.5秒后跳转 → 管理后台显示"欢迎进入管理后台，${username}！"

---

**V0.12.3 ui(frontend): 优化时间显示格式，精确到分钟级别**

日期：2025-07-11

**类型**: `ui`
**范围**: `frontend`

**说明**:
本次更新优化了管理员页面中用户注册时间和最后登录时间的显示格式，将原来只显示日期的格式改为精确到分钟的详细时间格式，提升了时间信息的精确性和实用性，便于管理员进行更细致的用户活动分析和管理。

**优化内容**:

*   **1. 用户管理表格时间显示优化 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserManagement.tsx`
    *   **注册时间列优化**:
        *   修改render函数使用toLocaleString方法
        *   添加详细的时间格式配置：year, month, day, hour, minute
        *   时间格式从 `2025/7/11` 优化为 `2025/07/11 15:30`
        *   列宽从120px调整为140px以适应新格式
    *   **最后登录列优化**:
        *   同样精确到分钟级别显示
        *   保持"从未登录"的友好提示
        *   统一时间格式标准

*   **2. 用户详情抽屉时间显示优化 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserDetailDrawer.tsx`
    *   **formatDate函数重构**:
        *   使用标准化的时间格式配置
        *   确保注册时间和最后登录时间显示一致性
        *   保持"从未"状态的用户友好显示
    *   **详情页面一致性**:
        *   与表格显示格式保持完全一致
        *   提供更详细的时间信息用于用户分析

*   **3. CSV导出时间格式优化 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserManagement.tsx`
    *   **导出数据格式统一**:
        *   CSV文件中的时间格式与界面显示保持一致
        *   确保导出数据的时间精确性
        *   便于外部数据分析和报表制作
    *   **数据完整性**:
        *   保持所有时间相关字段的格式一致性
        *   提升导出数据的专业性和可用性

**技术实现细节**:

*   **时间格式标准化**:
    *   使用JavaScript原生toLocaleString方法
    *   配置参数：`{ year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' }`
    *   确保中文本地化显示：`'zh-CN'`
    *   统一时间格式为：YYYY/MM/DD HH:MM

*   **界面适配优化**:
    *   表格列宽从120px增加到140px
    *   确保时间信息完整显示不被截断
    *   保持表格整体布局的美观性

*   **用户体验提升**:
    *   时间信息更加详细和精确
    *   便于管理员进行用户活动时间分析
    *   提升数据的可读性和实用性

**显示效果对比**:

*   **优化前**:
    *   注册时间：`2025/7/11` (仅日期)
    *   最后登录：`2025/7/11` (仅日期)
    *   信息精度：天级别

*   **优化后**:
    *   注册时间：`2025/07/11 15:30` (精确到分钟)
    *   最后登录：`2025/07/11 15:30` (精确到分钟)
    *   信息精度：分钟级别

**兼容性保证**:

*   **向下兼容**: 不影响现有的数据结构和API接口
*   **浏览器支持**: 使用标准JavaScript API，确保广泛兼容性
*   **本地化支持**: 正确处理中文时间格式显示

**测试验证**:

*   **构建验证**: 前端构建成功，无TypeScript错误 ✅
*   **格式验证**: 时间显示格式符合预期 ✅
*   **布局验证**: 表格列宽调整适配新格式 ✅
*   **导出验证**: CSV文件时间格式一致性 ✅

---

**V0.12.2 fix(backend): 修复用户登录时last_login字段未更新问题**

日期：2025-07-11

**类型**: `fix`
**范围**: `backend`

**说明**:
本次更新修复了V0.12.1版本中发现的关键问题：用户登录和管理员登录时，Django User模型的last_login字段没有被正确更新，导致管理员页面显示的"最后登录"信息始终为"从未登录"。通过在登录API中添加last_login字段更新逻辑，确保了用户登录时间的准确记录。

**问题分析**:

*   **根本原因**: Django的Token认证机制不会自动更新User模型的last_login字段
*   **影响范围**: 所有通过/api/auth/login/端点进行的用户登录和管理员登录
*   **表现症状**: 管理员页面用户列表中"最后登录"列始终显示"从未登录"
*   **发现方式**: 用户反馈管理员页面最后登录信息显示异常

**修复内容**:

*   **1. 修复登录API的last_login更新机制 (`fix`, `backend`)**:
    *   **文件**: `backend/accounts/views.py`
    *   **导入修复**:
        *   添加`from django.utils import timezone`导入
        *   移除错误的`update_last_login`导入尝试
    *   **LoginAPI.post()方法增强**:
        *   在用户认证成功后添加last_login字段更新逻辑
        *   使用`timezone.now()`获取当前UTC时间
        *   使用`user.save(update_fields=['last_login'])`进行高效的单字段更新
        *   确保普通用户和管理员登录都会触发时间更新

*   **2. 新增登录时间更新功能测试 (`test`, `backend`)**:
    *   **文件**: `backend/test_script/test_last_login_update.py` (新增)
    *   **测试覆盖**:
        *   普通用户登录时间更新验证
        *   管理员登录时间更新验证
        *   管理员API中last_login字段返回验证
        *   数据库字段更新的实时检查
    *   **测试结果**: 所有测试用例通过 ✅

**技术实现细节**:

*   **时间处理**: 使用Django的timezone.now()确保时区一致性
*   **数据库优化**: 使用update_fields参数只更新last_login字段，避免不必要的数据库写入
*   **兼容性保证**: 修复不影响现有的Token认证流程和API响应格式
*   **错误处理**: 导入错误修复，确保服务器正常启动

**修复验证**:

*   **功能验证**:
    *   ✅ 普通用户登录后last_login字段正确更新
    *   ✅ 管理员登录后last_login字段正确更新
    *   ✅ 管理员API正确返回last_login时间信息
    *   ✅ 前端管理员页面正确显示最后登录时间

*   **数据格式验证**:
    *   ✅ 时间格式符合ISO 8601标准 (2025-07-11T03:05:27.603191Z)
    *   ✅ 时区信息正确 (UTC+00:00)
    *   ✅ 前端时间显示格式化正常

*   **性能影响**:
    *   ✅ 登录响应时间无明显增加
    *   ✅ 数据库写入操作优化 (仅更新单字段)
    *   ✅ 不影响现有Token生成和验证流程

**修复效果**:

*   **用户体验改善**: 管理员可以准确查看用户的最后登录时间
*   **数据完整性**: 用户登录行为得到正确记录和追踪
*   **系统监控**: 为用户活跃度分析提供准确的数据基础
*   **安全审计**: 登录时间记录有助于安全事件的追溯和分析

**后续影响**:

*   **统计准确性**: 用户活跃度统计将基于准确的登录时间数据
*   **功能扩展**: 为后续的登录频率分析、用户行为分析奠定数据基础
*   **安全增强**: 异常登录时间模式检测成为可能

**技术债务清理**:

*   **导入规范**: 修复了错误的Django函数导入尝试
*   **代码一致性**: 统一了时间处理方式，使用Django标准的timezone工具
*   **测试覆盖**: 新增专门的登录时间更新测试，提高代码质量保障

---

**V0.12.1 refactor(fullstack): 简化管理员字段+实现实时统计更新机制**

日期：2025-07-11

**类型**: `refactor`
**范围**: `fullstack`

**说明**:
本次更新针对V0.12.0管理员用户管理系统进行了重要的优化和重构，主要解决了字段冗余问题和统计数据实时更新问题。通过简化用户字段结构和引入全局状态管理，提升了系统的一致性和用户体验。

**重构内容**:

*   **1. 简化用户字段结构 (`refactor`, `backend`)**:
    *   **文件**: `backend/accounts/serializers.py`
    *   **字段简化**:
        *   移除AdminUserCreateSerializer中的first_name、last_name、full_name字段
        *   移除AdminUserUpdateSerializer中的相关字段处理逻辑
        *   简化AdminUserSerializer字段定义，保持与用户注册页面一致
        *   优化create和update方法，移除冗余的Profile字段处理
    *   **数据结构统一**:
        *   统一前后端用户数据模型
        *   保留核心字段：username、email、权限字段、nfc_card_id
        *   确保与用户注册流程的字段一致性

*   **2. 前端用户管理界面简化 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserCreateModal.tsx`
    *   **表单简化**:
        *   移除名字、姓氏、全名输入字段
        *   简化表单布局和验证逻辑
        *   保持核心用户信息输入功能
    *   **文件**: `frontend/src/components/admin/UserEditModal.tsx`
    *   **编辑界面优化**:
        *   移除相关字段的编辑功能
        *   更新用户接口类型定义
        *   简化表单预填充逻辑
    *   **文件**: `frontend/src/components/admin/UserDetailDrawer.tsx`
    *   **详情展示优化**:
        *   移除冗余字段的显示逻辑
        *   简化用户信息展示结构
        *   保持清晰的信息层级

*   **3. 用户管理表格显示优化 (`refactor`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserManagement.tsx`
    *   **数据模型更新**:
        *   更新User接口定义，移除first_name、last_name字段
        *   移除本地UserStats接口，使用全局Context
        *   简化CSV导出数据结构
    *   **显示逻辑优化**:
        *   优化用户信息显示，直接使用email作为副标题
        *   移除对full_name的依赖引用
        *   保持表格功能完整性

**新增功能**:

*   **4. 全局统计数据状态管理 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/contexts/AdminStatsContext.tsx` (新增)
    *   **Context功能**:
        *   统一管理用户统计数据的全局状态
        *   提供refreshStats和updateStatsAfterUserOperation方法
        *   支持静默更新和错误处理机制
        *   实现跨组件的数据同步
    *   **Hook接口**:
        *   useAdminStats自定义Hook
        *   类型安全的Context使用
        *   完善的错误边界处理

*   **5. 实时统计更新机制 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **组件重构**:
        *   拆分为AdminDashboardContent内部组件
        *   使用AdminStatsProvider包装主组件
        *   集成全局统计数据Context
        *   保持30秒自动刷新机制
    *   **文件**: `frontend/src/components/admin/UserManagement.tsx`
    *   **实时更新集成**:
        *   使用useAdminStats Hook获取统计数据
        *   在用户操作后调用updateStatsAfterUserOperation
        *   移除本地fetchStats函数，统一使用Context管理

**修复内容**:

*   **6. 关键功能错误修复 (`fix`, `frontend`)**:
    *   **fetchStats未定义错误**:
        *   修复UserCreateModal和UserEditModal中的fetchStats调用错误
        *   替换为updateStatsAfterUserOperation调用
        *   确保用户操作成功后统计数据正确更新
    *   **TypeScript类型错误**:
        *   修复ReactNode导入类型错误
        *   使用type-only import解决verbatimModuleSyntax问题
        *   确保前端构建成功

*   **7. 用户体验问题修复 (`fix`, `frontend`)**:
    *   **统计数据实时更新**:
        *   解决右上角总用户数不实时刷新的问题
        *   实现用户操作后立即更新统计数据
        *   无需手动刷新页面即可看到最新数据
    *   **错误提示修复**:
        *   解决创建用户成功后仍显示失败提示的问题
        *   优化错误处理和成功反馈机制

**测试验证**:

*   **8. 功能完整性测试 (`test`, `backend`)**:
    *   **API接口验证**:
        *   所有管理员API接口测试通过 ✅
        *   用户CRUD操作正常工作
        *   批量操作和统计API功能正常
    *   **前端构建验证**:
        *   TypeScript编译无错误 ✅
        *   Vite构建成功 ✅
        *   所有组件正常渲染

**优化效果**:

*   **数据一致性**: 前后端用户字段完全统一，消除冗余
*   **实时体验**: 统计数据立即更新，无需手动刷新
*   **代码质量**: 移除重复代码，统一状态管理
*   **用户体验**: 简化界面，操作反馈更准确
*   **维护性**: 全局状态管理，便于扩展和维护
*   **类型安全**: 完善的TypeScript类型定义和检查

**技术亮点**:

*   **React Context模式**: 实现跨组件的状态共享和数据同步
*   **静默更新机制**: 用户操作后自动更新统计数据，不影响用户体验
*   **类型安全重构**: 完善的TypeScript类型定义和导入优化
*   **组件解耦**: 通过Context实现组件间的松耦合通信
*   **一致性设计**: 统一前后端数据模型和用户界面字段

---

**V0.12.0 feat(fullstack): 完整实现管理员用户管理系统前后端功能**

日期：2025-07-11

**类型**: `feat`
**范围**: `fullstack`

**说明**:
本次更新在V0.11.0后端API基础上，完整实现了管理员用户管理系统的前端界面，包括现代化的管理后台、用户CRUD操作、批量管理、实时统计等企业级功能。同时修复了多个关键的用户体验问题和技术警告，提供了完整的全栈解决方案。

**新增内容**:

*   **1. 完整的管理员前端界面 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AdminDashboard.tsx`
    *   **重构内容**:
        *   现代化管理后台布局（侧边栏导航+内容区域）
        *   实时用户统计显示（每30秒自动刷新）
        *   嵌套路由支持（用户管理、设备管理、日志管理等）
        *   响应式设计适配不同屏幕尺寸
    *   **新增功能**:
        *   管理员信息展示和下拉菜单
        *   统计数据实时更新机制
        *   清晰的导航结构和面包屑

*   **2. 用户管理核心组件 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserManagement.tsx`
    *   **核心功能**:
        *   用户列表展示（分页、搜索、过滤、排序）
        *   实时统计卡片（总用户、活跃用户、管理员、本周新增）
        *   批量操作支持（激活、停用、设权限、删除）
        *   CSV数据导出功能
        *   智能刷新和筛选重置
    *   **用户体验优化**:
        *   统计卡片loading状态，避免刷新时闪烁
        *   详细的操作反馈和错误提示
        *   危险操作的多重确认机制
        *   响应式表格设计

*   **3. 用户操作模态框组件 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserCreateModal.tsx`
    *   **创建用户功能**:
        *   完整的用户信息表单（基本信息、权限、Profile）
        *   实时表单验证和错误提示
        *   密码确认验证
        *   NFC卡号唯一性检查
    *   **文件**: `frontend/src/components/admin/UserEditModal.tsx`
    *   **编辑用户功能**:
        *   用户信息修改（除密码外的所有字段）
        *   权限级别调整
        *   Profile信息更新
        *   数据预填充和验证

*   **4. 用户详情展示组件 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/components/admin/UserDetailDrawer.tsx`
    *   **详情展示功能**:
        *   用户完整信息展示（头像、基本信息、统计数据）
        *   功能状态显示（人脸数据、NFC卡绑定状态）
        *   权限信息展示
        *   访问统计和账户状态
    *   **UI设计**:
        *   现代化抽屉式设计
        *   清晰的信息层级和视觉分组
        *   状态标签和图标指示

*   **5. 错误边界和异常处理 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/components/ErrorBoundary.tsx`
    *   **错误处理功能**:
        *   React错误边界组件
        *   友好的错误页面展示
        *   错误恢复操作（刷新页面、返回上页）
        *   错误信息记录和展示

*   **6. 路由配置和导航 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/App.tsx`
    *   **路由增强**:
        *   管理员嵌套路由配置
        *   错误边界包装保护
        *   预留扩展路由（设备管理、日志管理等）
    *   **导航优化**:
        *   清晰的路由层级结构
        *   默认页面和索引路由配置

**修复内容**:

*   **7. 关键功能修复 (`fix`, `frontend`)**:
    *   **删除功能修复**:
        *   修复Modal.confirm异步函数调用问题
        *   使用App.useApp()替代静态方法调用
        *   确保删除操作正确执行和反馈
    *   **批量操作修复**:
        *   修复批量删除确认对话框问题
        *   增强批量操作的错误处理和反馈

*   **8. React 19兼容性修复 (`fix`, `frontend`)**:
    *   **Antd组件兼容性**:
        *   使用App.useApp()替代静态message和notification
        *   修复Modal.confirm上下文问题
        *   更新destroyOnClose为destroyOnHidden
    *   **TypeScript错误修复**:
        *   修复23个TypeScript编译错误
        *   清理未使用的导入和变量
        *   添加正确的类型定义

*   **9. 用户体验优化 (`ui`, `frontend`)**:
    *   **统计卡片优化**:
        *   添加loading状态，避免刷新时组件消失
        *   使用可选链操作符确保数据安全访问
        *   保持组件持续显示，只更新数值
    *   **操作反馈增强**:
        *   详细的成功/失败通知信息
        *   操作进度和状态指示
        *   危险操作的警告和确认机制

**测试和验证**:

*   **10. API接口测试 (`test`, `backend`)**:
    *   **文件**: `backend/test_script/test_admin_apis.py`
    *   **测试覆盖**:
        *   8个核心管理员API全面测试
        *   创建、读取、更新、删除操作验证
        *   批量操作和统计API测试
        *   错误处理和边界条件测试
    *   **测试结果**: 所有API接口测试通过 ✅

**修复效果**:

*   **完整的用户管理**: 创建、编辑、删除、批量操作
*   **实时数据统计**: 用户数量、活跃度、功能使用情况
*   **高级搜索过滤**: 多维度筛选和智能搜索
*   **数据导出功能**: CSV格式用户数据导出
*   **安全操作机制**: 权限验证、操作确认、错误处理
*   **现代化界面**: 响应式设计、流畅交互、清晰导航
*   **企业级特性**: 批量操作、审计日志、统计分析

---

**V0.11.0 feat(backend): 实现完整的管理员用户管理功能**

日期：2025-07-11

**类型**: `feat`
**范围**: `backend`

**说明**:
本次更新为智能门禁系统实现了完整的管理员用户管理功能，包括用户CRUD操作、批量管理、统计分析等企业级管理功能。通过新增专用的管理员API接口和序列化器，为后续的管理员前端界面提供了强大的后端支持。

**新增内容**:

*   **1. 扩展管理员专用序列化器 (`feat`, `backend`)**:
    *   **文件**: `backend/accounts/serializers.py`
    *   **新增序列化器**:
        *   `AdminUserSerializer` - 管理员视角的用户详细信息序列化器
        *   `AdminUserCreateSerializer` - 管理员创建用户的序列化器
        *   `AdminUserUpdateSerializer` - 管理员更新用户的序列化器
        *   `UserListFilterSerializer` - 用户列表过滤参数序列化器
        *   `AdminUserBatchSerializer` - 批量操作用户的序列化器
    *   **功能特性**:
        *   包含用户完整信息（基本信息、权限、Profile、统计数据）
        *   支持人脸数据注册状态和访问日志统计
        *   完善的数据验证（密码确认、NFC卡号唯一性等）
        *   异常处理增强，确保数据访问安全性

*   **2. 开发管理员专用API视图 (`feat`, `backend`)**:
    *   **文件**: `backend/accounts/views.py`
    *   **新增API视图**:
        *   `AdminUserListAPI` - 用户列表获取（支持搜索、过滤、分页、排序）
        *   `AdminUserCreateAPI` - 创建新用户
        *   `AdminUserDetailAPI` - 获取用户详情
        *   `AdminUserUpdateAPI` - 更新用户信息
        *   `AdminUserDeleteAPI` - 删除用户（含安全检查）
        *   `AdminUserBatchAPI` - 批量操作用户
        *   `AdminUserStatsAPI` - 用户统计信息
    *   **核心功能**:
        *   支持多维度用户过滤（权限、状态、人脸数据、NFC卡等）
        *   智能搜索（用户名、邮箱、姓名等字段）
        *   分页器配置（20条/页，最大100条/页）
        *   安全权限控制（防止删除自己、超级管理员保护）

*   **3. 批量操作功能 (`feat`, `backend`)**:
    *   **支持操作类型**:
        *   `activate` - 批量激活用户
        *   `deactivate` - 批量停用用户
        *   `make_staff` - 批量设为管理员
        *   `remove_staff` - 批量取消管理员
        *   `delete` - 批量删除用户
    *   **安全机制**:
        *   防止对自己执行批量操作
        *   超级管理员权限保护
        *   详细的操作结果反馈
        *   失败用户列表和原因记录

*   **4. 用户统计分析功能 (`feat`, `backend`)**:
    *   **统计维度**:
        *   用户数量统计（总数、活跃、管理员、超级管理员）
        *   功能使用统计（人脸数据、NFC卡绑定）
        *   注册趋势分析（今日、本周、本月新用户）
        *   活跃度分析（基于最后登录时间）
    *   **数据展示**:
        *   实时统计数据生成
        *   时间戳标记确保数据时效性
        *   结构化JSON格式便于前端展示

*   **5. 配置管理员API路由 (`feat`, `backend`)**:
    *   **文件**: `backend/accounts/urls.py`
    *   **新增路由**:
        *   `/api/admin/users/` - 用户列表和创建
        *   `/api/admin/users/<id>/` - 用户详情
        *   `/api/admin/users/<id>/update/` - 用户更新
        *   `/api/admin/users/<id>/delete/` - 用户删除
        *   `/api/admin/users/batch/` - 批量操作
        *   `/api/admin/users/stats/` - 统计信息
    *   **路由特性**:
        *   RESTful API设计规范
        *   清晰的URL命名约定
        *   完整的CRUD操作支持

*   **6. 数据库查询优化 (`feat`, `backend`)**:
    *   **性能优化**:
        *   使用`select_related`和`prefetch_related`优化关联查询
        *   减少数据库查询次数，提升API响应速度
        *   支持大数据量用户列表的高效分页
    *   **查询增强**:
        *   多字段搜索支持
        *   复杂过滤条件组合
        *   灵活的排序机制

**API接口规范**:

*   **权限控制**: 所有管理员API均需要`IsAdminUser`权限
*   **数据格式**: 统一使用JSON格式进行数据交换
*   **错误处理**: 完善的HTTP状态码和错误信息返回
*   **分页支持**: 标准的分页参数和响应格式
*   **搜索过滤**: 支持多种查询参数组合

**后续支持**:
本次后端API开发为管理员前端界面提供了完整的数据支持，包括用户列表展示、创建编辑表单、批量操作界面、统计图表等功能的实现基础。

---

**V0.10.2.1 test(backend): 新增test用户人脸数据验证测试脚本**

日期：2025-07-10

**类型**: `test`
**范围**: `backend`

**说明**:
本次更新新增了专门用于验证test用户人脸数据有效性的测试脚本。该脚本提供完整的人脸数据提取、特征匹配和结果分析功能，用于验证存储的人脸向量数据是否有效以及人脸匹配算法是否正常工作。

**新增内容**:

*   **1. 新增人脸数据验证测试脚本 (`test`, `backend`)**:
    *   **文件**: `backend/test_script/test_user_face_data_validation.py`
    *   **功能**: 
        *   专门针对test用户(test/testtest)的人脸数据验证
        *   从数据库中提取存储的人脸向量数据
        *   加载测试图片进行人脸特征提取
        *   计算余弦距离和欧几里得距离进行相似度分析
        *   使用0.50阈值进行匹配判断
        *   输出详细的验证结果和数据分析报告

*   **2. 完整的数据分析功能 (`test`, `backend`)**:
    *   **向量数据分析**: 
        *   显示向量维度、类型、数值范围
        *   计算向量均值和标准差
        *   验证数据完整性和有效性
    *   **相似度计算**: 
        *   余弦相似度和余弦距离计算
        *   欧几里得距离计算
        *   点积和向量范数分析
    *   **匹配结果判断**: 
        *   基于VGG-Face模型阈值(0.50)的匹配判断
        *   清晰的匹配/不匹配状态显示
        *   详细的距离数值输出

*   **3. 多图片测试支持 (`test`, `backend`)**:
    *   **测试图片配置**: 
        *   支持test_face.jpg、test_face2.jpg、test_face3.jpg
        *   自动检测图片文件存在性
        *   逐个处理并分析每张测试图片
    *   **错误处理**: 
        *   图片文件不存在的处理
        *   人脸检测失败的处理
        *   特征提取异常的处理
    *   **结果汇总**: 
        *   总测试数、成功处理数、匹配成功数统计
        *   详细的每张图片测试结果列表

*   **4. 用户友好的输出格式 (`test`, `backend`)**:
    *   **进度显示**: 
        *   清晰的步骤划分和进度提示
        *   emoji图标增强可读性
        *   详细的处理状态反馈
    *   **数据展示**: 
        *   格式化的数值显示(保留6位小数)
        *   表格化的结果汇总
        *   彩色状态指示(✅❌⚠️)
    *   **调试信息**: 
        *   详细的错误信息和异常堆栈
        *   完整的数据分析过程记录

**测试脚本特点**:
*   **专用性**: 专门为test用户设计，无需修改即可使用
*   **完整性**: 覆盖从数据提取到结果分析的完整流程
*   **可靠性**: 完善的错误处理和异常捕获机制
*   **可读性**: 清晰的输出格式和详细的分析报告
*   **可扩展性**: 易于修改测试图片和用户配置

**使用方法**:
```bash
cd backend
source venv/bin/activate
python test_script/test_user_face_data_validation.py
```

**验证目标**:

*   验证test用户的人脸数据是否正确存储在数据库中
*   验证人脸特征提取功能是否正常工作
*   验证人脸匹配算法的准确性和有效性
*   验证存储的向量数据的完整性和有效性
*   提供详细的性能分析和调试信息

**技术实现**:

*   **数据库访问**: 直接从FaceData模型提取存储的人脸向量
*   **特征提取**: 使用face_db.services.represent_face进行人脸特征提取
*   **相似度计算**: 实现余弦距离和欧几里得距离的双重计算
*   **结果分析**: 提供多维度的数据分析和可视化输出

---

**V0.10.2 fix(backend/frontend): 修复用户注册数据库冲突和注册页面UI优化**

日期：2025-07-10

**类型**: `fix`
**范围**: `backend`, `frontend`

**说明**:
本次更新修复了用户注册时的数据库完整性错误和注册页面的UI布局问题。通过优化Django信号处理器、改进前端用户体验和统一输入框布局，确保了用户注册功能的稳定性和界面的一致性。

**修复内容**:

*   **1. 修复用户注册数据库冲突 (`fix`, `backend`)**:
    *   **文件**: `backend/accounts/models.py`
    *   **问题**: Profile重复创建导致UNIQUE constraint failed错误
    *   **修复**: 
        *   合并两个post_save信号处理器为单一处理器
        *   使用get_or_create()方法防止重复创建Profile
        *   优化信号处理逻辑，确保Profile唯一性

*   **2. 优化用户注册序列化器 (`fix`, `backend`)**:
    *   **文件**: `backend/accounts/serializers.py`
    *   **问题**: 序列化器手动创建Profile与信号处理器冲突
    *   **修复**: 
        *   移除序列化器中的手动Profile创建逻辑
        *   依赖信号处理器自动创建Profile
        *   添加详细注释说明修复原因

*   **3. 优化注册成功用户体验 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AuthPage.tsx`
    *   **改进**: 
        *   使用notification.success()显示个性化成功提示
        *   包含用户名的欢迎信息，提升用户体验
        *   注册成功后自动切换到登录面板并预填用户名
        *   自动清空注册表单，避免数据残留

*   **4. 修复注册页面输入框布局不一致 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AuthPage.tsx`
    *   **问题**: 密码输入框比用户名和邮箱输入框更长
    *   **修复**: 
        *   统一所有输入框的布局结构和宽度计算
        *   使用calc(100% - 28px)确保所有输入框宽度一致
        *   优化验证图标位置，移至输入框外部显示
        *   保持输入框圆角完整性

*   **5. 实现密码验证状态可视化 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/pages/AuthPage.tsx`
    *   **功能**: 
        *   密码长度验证：>=8位显示绿色勾号，否则显示红色叉号
        *   确认密码匹配验证：匹配显示绿色勾号，不匹配显示红色叉号
        *   验证图标位于输入框右侧外部，不影响输入框样式
        *   实时响应用户输入，提供即时反馈

*   **6. 新增用户注册测试套件 (`test`, `backend`)**:
    *   **文件**: `backend/test_script/test_user_registration_fix.py`
    *   **功能**: 
        *   直接用户创建测试，验证信号处理器正常工作
        *   API序列化器注册测试，模拟前端注册流程
        *   并发创建测试，确保高并发场景下的数据一致性
        *   Profile信号处理器行为测试，验证更新逻辑

**修复效果**:
*   **注册稳定性**: 解决了用户注册时的数据库完整性错误
*   **用户体验**: 注册成功后有明确的反馈和流畅的流程引导
*   **界面一致性**: 所有输入框宽度统一，布局整齐美观
*   **实时反馈**: 密码验证状态实时显示，提升交互体验
*   **代码质量**: 优化信号处理逻辑，提供完整的测试覆盖

---

**V0.10.1 fix(backend/frontend): 修复人脸数据管理核心问题和数据库约束**

日期：2025-07-10

**类型**: `fix`
**范围**: `backend`, `frontend`

**说明**:
本次更新修复了人脸数据管理系统中的关键问题，包括数据库约束缺失、前端文件上传失败、API序列化器字段缺失等核心问题。通过数据库重构、API修复和前端逻辑优化，确保了系统的数据一致性和功能稳定性。

**修复内容**:

*   **1. 修复数据库约束问题 (`fix`, `backend`)**:
    *   **文件**: `backend/face_db/models.py`
    *   **问题**: 用户可能有多条人脸记录，缺少唯一性约束
    *   **修复**: 
        *   将ForeignKey改为OneToOneField，确保每个用户最多一条人脸记录
        *   添加updated_at字段，支持更新时间跟踪
        *   优化模型元数据和字段描述
        *   确保数据库级别的唯一性约束

*   **2. 创建数据库迁移文件 (`fix`, `backend`)**:
    *   **文件**: 
        *   `backend/face_db/migrations/0002_add_fields.py`
        *   `backend/face_db/migrations/0003_change_to_onetoone.py`
    *   **内容**:
        *   分步骤安全迁移：先添加字段，再修改关系
        *   自动清理重复数据，保留每个用户最新记录
        *   完整的字段更新和模型元数据修改

*   **3. 修复API序列化器字段缺失 (`fix`, `backend`)**:
    *   **文件**: `backend/face_db/serializers.py`
    *   **问题**: FaceDataSerializer缺少updated_at字段
    *   **修复**: 
        *   添加updated_at到fields列表
        *   设置为只读字段
        *   确保API返回完整的时间信息

*   **4. 增强人脸处理服务 (`fix`, `backend`)**:
    *   **文件**: `backend/face_db/services.py`
    *   **修复**:
        *   添加PIL图片验证，确保文件完整性
        *   增强文件存在和大小检查
        *   优化错误处理和日志记录
        *   提供更详细的DeepFace处理信息

*   **5. 修复API视图逻辑 (`fix`, `backend`)**:
    *   **文件**: `backend/face_db/views.py`
    *   **问题**: 
        *   图片保存逻辑错误导致文件损坏
        *   update_or_create逻辑不适配OneToOneField
        *   错误处理不完善
    *   **修复**:
        *   修复Base64图片解码和保存逻辑
        *   适配OneToOneField的创建/更新逻辑
        *   增强错误处理和日志记录
        *   优化临时文件管理

*   **6. 修复前端文件上传问题 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/pages/FaceManagementPage.tsx`
    *   **问题**: 
        *   Ant Design Upload组件file.originFileObj为空
        *   前端无响应，上传失败
        *   状态更新不及时
    *   **修复**:
        *   实现三层备选文件获取方案
        *   优化文件类型检查和错误处理
        *   增强状态更新和UI同步机制
        *   添加详细的调试日志

*   **7. 优化前端状态同步 (`fix`, `frontend`)**:
    *   **修复内容**:
        *   立即更新本地状态，避免延迟
        *   添加缓存破坏机制，确保数据最新
        *   延迟刷新策略，避免竞态条件
        *   增强UI显示，包含更新时间和数据ID

*   **8. 新增测试和调试工具 (`test`, `backend`)**:
    *   **文件**: 
        *   `backend/test_script/test_face_data_integrity.py`
        *   `backend/test_script/test_face_upload.py`
        *   `backend/test_script/test_real_face.py`
        *   `backend/test_script/verify_test_scripts.py`
        *   `backend/test_script/debug_frontend_issue.py`
    *   **功能**:
        *   完整性测试：验证数据库约束和API逻辑
        *   上传测试：模拟真实文件上传场景
        *   调试工具：前后端问题诊断和排查
        *   脚本验证：确保测试工具正常工作

**修复效果**:
*   **数据一致性**: 确保每个用户最多只有一条人脸记录
*   **功能稳定性**: 人脸上传和更新功能完全正常
*   **状态同步**: 前端界面实时反映最新的人脸数据状态
*   **错误处理**: 完善的异常捕获和用户友好提示
*   **调试能力**: 提供完整的测试和调试工具集
*   **代码质量**: 优化错误处理、日志记录和代码结构

**技术改进**:
*   **数据库设计**: OneToOneField确保数据唯一性
*   **API设计**: 完整的字段序列化和错误响应
*   **文件处理**: 安全的图片解码、验证和存储
*   **前端交互**: 多层备选方案确保文件获取成功
*   **状态管理**: 立即更新+延迟刷新的双重保障机制

---

**V0.10.0 feat(frontend): 实现完整的人脸数据管理用户界面**

日期：2025-07-10

**类型**: `feat`
**范围**: `frontend`

**说明**:
本次更新实现了完整的用户人脸数据管理界面，提供了直观易用的单张图片上传、实时预览、进度显示等功能。通过优化用户体验设计和完善的状态管理，用户现在可以轻松地注册和更新人脸数据，为门禁系统的人脸识别功能提供了友好的前端交互界面。

**实现细节**:
*   **1. 重构人脸管理页面组件 (`feat`, `frontend`)**:
    *   **文件**: `frontend/src/pages/FaceManagementPage.tsx`
    *   **内容**:
        *   完全重构FaceManagementPage组件，提升用户体验和功能完整性
        *   新增多个状态管理：上传进度、预览图片、当前人脸数据、加载状态
        *   实现响应式布局设计，支持桌面端和移动端访问
        *   添加完整的错误处理和用户反馈机制

*   **2. 实现单张图片上传功能 (`feat`, `frontend`)**:
    *   **功能特性**:
        *   限制用户只能上传一张人脸图片（maxCount: 1）
        *   支持JPG/PNG格式验证，文件大小限制2MB
        *   支持拖拽上传和点击上传两种交互方式
        *   自动替换已选择的图片，确保单张图片管理
        *   完善的文件格式和大小验证，提供清晰的错误提示

*   **3. 添加实时图片预览功能 (`feat`, `frontend`)**:
    *   **预览特性**:
        *   选择图片后立即生成Base64预览，无需上传即可查看
        *   右侧独立预览区域，采用卡片式设计
        *   支持点击查看大图功能，使用Ant Design Image组件
        *   显示文件名和基本信息，提供完整的文件详情
        *   美观的预览框架，包含边框、圆角、背景色等视觉优化

*   **4. 实现上传进度和状态显示 (`feat`, `frontend`)**:
    *   **进度管理**:
        *   实时上传进度条，支持0-100%进度显示
        *   渐变色进度条设计，提升视觉体验
        *   分阶段进度提示：上传阶段(0-90%)和处理阶段(90-100%)
        *   上传完成后自动清除进度状态
        *   支持上传过程中的取消和错误处理

*   **5. 完善用户状态管理 (`feat`, `frontend`)**:
    *   **状态显示**:
        *   自动获取用户当前人脸数据状态
        *   已注册状态：显示注册时间和成功提示
        *   未注册状态：显示警告提示和操作指引
        *   智能按钮文本：根据状态显示"注册人脸数据"或"更新人脸数据"
        *   操作完成后自动刷新状态数据

*   **6. 优化用户交互体验 (`ui`, `frontend`)**:
    *   **交互优化**:
        *   左右分栏布局：上传区域和预览区域分离
        *   清除选择功能：用户可以重新选择图片
        *   可关闭的状态消息：成功和错误提示支持手动关闭
        *   禁用状态管理：上传过程中禁用相关操作按钮
        *   图标和视觉元素：使用UserOutlined、EyeOutlined等图标提升界面友好性

*   **7. 集成后端API调用 (`feat`, `frontend`)**:
    *   **API集成**:
        *   GET /api/faces/ - 获取用户当前人脸数据
        *   POST /api/faces/register/ - 注册/更新人脸数据
        *   完整的Token认证支持
        *   Base64图片编码和传输
        *   上传进度回调处理
        *   错误响应解析和用户友好提示

*   **8. 添加响应式设计支持 (`ui`, `frontend`)**:
    *   **布局适配**:
        *   最大宽度800px的居中布局
        *   Flex布局实现左右分栏
        *   移动端友好的间距和尺寸
        *   自适应的图片预览区域
        *   统一的卡片式设计语言

**改进效果**:
*   **用户体验**: 提供直观易用的人脸数据管理界面，操作流程清晰简单
*   **视觉设计**: 现代化的UI设计，符合Ant Design设计规范
*   **功能完整**: 支持查看当前状态、上传新图片、实时预览、进度跟踪等完整流程
*   **错误处理**: 完善的验证和错误提示，帮助用户正确操作
*   **性能优化**: 客户端图片预览，减少服务器请求
*   **响应式**: 支持不同设备和屏幕尺寸的访问

---

**V0.9.1 chore(frontend): 优化Vite开发服务器配置以支持网络访问**

日期：2025-07-10

**类型**: `chore`
**范围**: `frontend`

**说明**:
本次更新优化了前端Vite开发服务器的配置，提升了开发体验和网络访问的稳定性。通过配置端口管理和访问控制选项，确保开发服务器能够在不同网络环境下稳定运行，支持多设备访问测试。

**实现细节**:
*   **1. 优化Vite服务器配置 (`chore`, `frontend`)**:
    *   **文件**: `frontend/vite.config.ts`
    *   **内容**:
        *   添加 `strictPort: false` 配置，当端口被占用时自动尝试下一个可用端口
        *   设置 `open: false` 配置，防止自动打开浏览器干扰开发流程
        *   保持 `host: '0.0.0.0'` 配置，确保支持外部网络访问
        *   完善中文注释，提升配置文件的可读性

**改进效果**:
*   **开发稳定性**: 端口冲突时自动切换，避免启动失败
*   **网络兼容性**: 支持172.31.x.x、192.168.x.x等不同网段的网络访问
*   **多设备测试**: 同一网络内的手机、平板等设备可以访问开发服务器
*   **开发体验**: 避免自动打开浏览器的干扰，让开发者自主控制

**网络访问支持**:
*   **本地访问**: http://localhost:5173/
*   **网络访问**: http://[当前机器IP]:5173/ (如 http://**************:5173/)
*   **跨设备测试**: 支持同一网络内的移动设备和其他电脑访问

---

**V0.9.0 feat(security): 实现WebSocket身份验证和HTTPS/CORS安全配置**

日期：2025-07-10

**类型**: `feat`
**范围**: `security`

**说明**:
本次更新实现了完整的WebSocket身份验证机制和HTTPS/CORS安全配置，显著提升了实时通信的安全性。通过Token认证、设备密钥验证、完善的CORS策略和HTTPS支持，确保了前后端通信和硬件设备连接的安全性，为生产环境部署提供了企业级的安全保障。

**实现细节**:
*   **1. 实现WebSocket身份验证系统 (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`, `backend/access_control/middleware.py`
    *   **内容**:
        *   重构DeviceConsumer为异步消费者，支持Token和设备密钥双重认证
        *   新增TokenAuthMiddleware中间件，实现WebSocket连接的身份验证
        *   添加用户Token认证和ESP32设备密钥认证机制
        *   实现连接拒绝机制，未认证连接自动断开(4001/4003错误码)
        *   添加完整的事件处理：NFC扫描、人脸检测、门禁状态、心跳检测

*   **2. 新增多类型WebSocket消费者 (`feat`, `backend`)**:
    *   **文件**: `backend/access_control/consumers.py`, `backend/access_control/routing.py`
    *   **内容**:
        *   新增AdminMonitorConsumer，支持管理员实时监控和设备控制
        *   新增UserDashboardConsumer，为用户提供个人仪表板实时数据
        *   实现分组消息广播机制，支持设备组、管理员组、用户组通信
        *   添加设备命令发送、访问日志查询、用户资料获取等功能

*   **3. 更新ASGI配置和路由 (`feat`, `backend`)**:
    *   **文件**: `backend/config/asgi.py`, `backend/access_control/routing.py`
    *   **内容**:
        *   集成TokenAuthMiddleware到ASGI应用栈
        *   新增多个WebSocket路由：设备连接、管理员监控、用户仪表板
        *   配置Channel Layers支持，为生产环境Redis扩展做准备

*   **4. 实现完整的CORS配置 (`feat`, `backend`)**:
    *   **文件**: `backend/config/settings.py`, `backend/requirements.txt`
    *   **内容**:
        *   新增django-cors-headers依赖和中间件配置
        *   实现环境变量控制的CORS策略，支持开发和生产环境分离
        *   配置CORS_ALLOWED_ORIGINS、CORS_ALLOW_CREDENTIALS等安全选项
        *   添加WebSocket CORS支持(CORS_ALLOW_WEBSOCKETS)
        *   实现开发环境localhost自动包含，生产环境严格域名控制

*   **5. 配置HTTPS和安全头支持 (`feat`, `backend`)**:
    *   **文件**: `backend/config/settings.py`
    *   **内容**:
        *   新增USE_HTTPS环境变量控制HTTPS功能
        *   实现自动化安全头配置：SSL重定向、HSTS、XSS保护、内容类型保护
        *   配置安全Cookie设置：SESSION_COOKIE_SECURE、CSRF_COOKIE_SECURE
        *   添加反向代理支持(SECURE_PROXY_SSL_HEADER)
        *   实现开发和生产环境的差异化安全策略

*   **6. 更新前端代理配置 (`feat`, `frontend`)**:
    *   **文件**: `frontend/vite.config.ts`
    *   **内容**:
        *   添加WebSocket代理支持(/ws路径)
        *   完善媒体文件代理配置(/media路径)
        *   优化构建配置，支持代码分割和生产环境优化
        *   添加中文注释，提升代码可读性

*   **7. 完善环境变量配置 (`feat`, `backend`)**:
    *   **文件**: `backend/.env.example`, `backend/.env.production.example`
    *   **内容**:
        *   新增HTTPS配置选项(USE_HTTPS)
        *   完善CORS配置模板(CORS_ALLOWED_ORIGINS)
        *   添加WebSocket和Redis配置选项
        *   提供生产环境SSL/TLS配置指导
        *   包含设备认证密钥配置示例

*   **8. 扩展安全检查工具 (`feat`, `backend`)**:
    *   **文件**: `backend/check_security.py`
    *   **内容**:
        *   新增CORS配置检查功能
        *   添加WebSocket配置验证(Channel Layers、ASGI应用)
        *   实现HTTPS配置检查(SSL重定向、HSTS设置)
        *   扩展安全评分系统，从6项增加到9项检查
        *   提供WebSocket安全和HTTPS配置的详细指导

*   **9. 创建WebSocket测试套件 (`test`, `backend`)**:
    *   **文件**: `backend/test_script/test_websocket.py`, `backend/test_script/test_websocket_simple.py`
    *   **内容**:
        *   实现完整的WebSocket认证测试框架
        *   支持用户、设备、管理员三种认证类型测试
        *   添加无认证连接拒绝测试
        *   提供配置验证和模块导入测试
        *   创建异步测试支持，兼容Django Channels

*   **10. 完善测试脚本管理 (`chore`, `backend`)**:
    *   **文件**: `backend/test_script/README.md`
    *   **内容**:
        *   统一测试脚本到test_script目录
        *   创建详细的测试脚本使用指南
        *   提供快速测试、故障排除和CI/CD集成说明
        *   包含WebSocket、人脸识别、安全配置的完整测试流程

*   **11. 创建HTTPS配置指南 (`docs`, `backend`)**:
    *   **文件**: `docs/backend/HTTPS_SETUP.md`
    *   **内容**:
        *   提供Let's Encrypt、自签名证书、Nginx反向代理三种HTTPS配置方案
        *   包含WebSocket安全连接配置(WSS)
        *   提供前端和ESP32设备的安全连接示例代码
        *   包含SSL测试、安全头检查、故障排除的完整指导

*   **12. 优化依赖管理 (`chore`, `backend`)**:
    *   **文件**: `backend/requirements.txt`
    *   **内容**:
        *   新增django-cors-headers依赖
        *   添加websockets测试支持
        *   保持版本兼容性和安全性

**改进效果**:
*   **通信安全**: WebSocket连接现在需要身份验证，防止未授权访问
*   **设备安全**: ESP32等硬件设备通过密钥认证，确保设备合法性
*   **跨域安全**: 完善的CORS策略防止恶意跨域请求
*   **传输安全**: HTTPS支持确保数据传输加密
*   **实时监控**: 管理员可以实时监控所有设备和访问事件
*   **用户体验**: 用户仪表板提供实时的个人访问数据
*   **开发体验**: 完整的测试套件和详细的配置文档
*   **生产就绪**: 支持Redis扩展和企业级部署配置

**安全检查结果**:
*   开发环境: 3/9项通过（新增CORS、WebSocket、HTTPS检查）
*   生产环境配置: 支持完整的HTTPS和安全头配置
*   WebSocket认证: 支持Token和设备密钥双重认证机制

**技术架构提升**:
*   **实时通信**: 从无认证WebSocket升级为企业级安全实时通信
*   **多端支持**: 同时支持Web用户、管理员和IoT设备的安全连接
*   **扩展性**: Channel Layers架构支持水平扩展和负载均衡
*   **监控能力**: 实时事件广播和设备状态监控
*   **安全合规**: 符合现代Web应用安全标准和最佳实践

---

**V0.8.0 feat(security): 实现生产级安全配置管理系统**

日期：2025-07-10

**类型**: `feat`
**范围**: `security`

**说明**:
本次更新实现了完整的生产级安全配置管理系统，解决了Django项目中的关键安全隐患，包括SECRET_KEY硬编码、DEBUG模式控制、ALLOWED_HOSTS配置等问题。通过环境变量管理、自动化安全检查和完善的部署工具，显著提升了系统的安全性和生产环境适配能力。

**实现细节**:
*   **1. 修复核心安全配置问题 (`fix`, `backend`)**:
    *   **文件**: `backend/config/settings.py`
    *   **内容**:
        *   移除硬编码的SECRET_KEY，使用环境变量`DJANGO_SECRET_KEY`管理
        *   通过环境变量`DJANGO_DEBUG`控制DEBUG模式，生产环境自动设置为False
        *   使用环境变量`DJANGO_ALLOWED_HOSTS`管理允许的主机列表
        *   添加完整的安全头配置（HTTPS重定向、HSTS、XSS保护等）
        *   实现生产环境和开发环境的自动化安全策略切换

*   **2. 实现环境变量管理系统 (`feat`, `backend`)**:
    *   **文件**: `backend/requirements.txt`, `backend/config/settings.py`
    *   **内容**:
        *   新增python-dotenv依赖，支持.env文件加载
        *   创建完整的环境变量配置体系
        *   实现开发和生产环境的配置分离

*   **3. 创建安全配置模板和示例 (`feat`, `backend`)**:
    *   **文件**: `backend/.env.example`, `backend/.env.production.example`, `backend/.env.test.production`
    *   **内容**:
        *   提供开发环境配置模板，包含安全的开发密钥
        *   创建生产环境配置模板，包含完整的安全配置指导
        *   提供测试用生产环境配置，便于安全验证

*   **4. 开发安全检查工具 (`feat`, `backend`)**:
    *   **文件**: `backend/check_security.py`
    *   **内容**:
        *   实现自动化安全配置检查脚本
        *   检查SECRET_KEY安全性、DEBUG模式、ALLOWED_HOSTS配置
        *   验证数据库配置和安全头设置
        *   提供详细的安全评分和改进建议

*   **5. 创建部署检查脚本 (`feat`, `backend`)**:
    *   **文件**: `backend/deploy_check.sh`
    *   **内容**:
        *   实现全面的部署前检查流程
        *   包含环境文件验证、依赖检查、Django系统检查
        *   支持开发和生产环境的差异化检查
        *   提供部署清单和操作指导

*   **6. 完善日志和安全配置 (`feat`, `backend`)**:
    *   **文件**: `backend/config/settings.py`
    *   **内容**:
        *   配置完整的日志系统，支持文件和控制台输出
        *   添加文件上传安全限制（5MB大小限制）
        *   实现CORS配置管理
        *   配置生产环境安全Cookie和会话管理

*   **7. 更新安全相关配置文件 (`chore`, `backend`)**:
    *   **文件**: `backend/.gitignore`
    *   **内容**:
        *   添加环境变量文件到忽略列表
        *   包含日志文件、密钥文件等敏感信息的保护
        *   完善备份文件和临时文件的忽略规则

*   **8. 创建完整的安全文档 (`docs`)**:
    *   **文件**: `docs/SECURITY_SETUP.md`, `docs/SECURITY_FIX_SUMMARY.md`
    *   **内容**:
        *   提供详细的安全配置指南和最佳实践
        *   包含开发和生产环境的配置说明
        *   提供故障排除和维护指导
        *   总结所有安全修复内容和使用方法

**改进效果**:
*   **安全性提升**: 解决了所有关键的Django安全配置问题，符合生产环境部署标准
*   **环境管理**: 实现了开发、测试、生产环境的完全分离和自动化管理
*   **部署安全**: 提供了完整的部署前安全检查和验证工具
*   **开发体验**: 简化了安全配置流程，提供了自动化的检查和指导工具
*   **文档完善**: 提供了详尽的安全配置文档和操作指南
*   **合规性**: 符合Django官方安全检查清单的所有要求

**安全检查结果**:
*   开发环境: 2/6项通过（符合开发环境预期）
*   生产环境测试: 5/6项通过（仅数据库建议优化）
*   Django内置检查: 所有安全警告已解决

---

**V0.7.1 fix(frontend): 修复前端编译错误**

日期：2025-07-10

**类型**: `fix`
**范围**: `frontend`

**说明**:
本次更新修复了由于在代码中导入了但未使用的组件而导致的 TypeScript 编译错误，确保了前端项目可以成功构建。

**实现细节**:
*   **1. 修复编译时未使用的导入变量问题 (`fix`, `frontend`)**:
    *   **前端文件**:
        *   `frontend/src/components/SecuritySettings.tsx`
        *   `frontend/src/pages/FaceManagementPage.tsx`
        *   `frontend/src/pages/UserDashboard.tsx`
    *   **内容**:
        *   移除了在 `SecuritySettings.tsx` 中未使用的 `Space` 组件导入。
        *   移除了在 `FaceManagementPage.tsx` 中未使用的 `Spin` 组件导入。
        *   移除了在 `UserDashboard.tsx` 中未使用的 `Title` 解构赋值和 `Typography` 组件导入。

**改进效果**:
*   **开发体验**: 解决了前端编译时出现的 `TS6133` 错误，使得构建流程可以顺利完成。
*   **代码质量**: 清理了冗余的代码，提升了代码的整洁度。

---

**V0.7.0 feat(fullstack): 完善用户管理功能并修复关键用户体验问题**

日期：2025-07-10

**类型**: `feat`
**范围**: `fullstack`

**说明**:
本次更新全面完善了用户管理功能，实现了头像上传、用户名修改、密码修改等核心功能，并解决了多个影响用户体验的关键问题。通过前后端协同优化，显著提升了系统的可用性和用户满意度。

**实现细节**:
*   **1. 实现完整的头像上传功能 (`feat`, `backend`, `frontend`)**:
    *   **后端文件**: `backend/accounts/views.py`, `backend/accounts/urls.py`, `backend/config/urls.py`
    *   **前端文件**: `frontend/src/components/AccountSettings.tsx`
    *   **内容**: 
        *   新增 `UserSelfProfileUpdateAPI` 视图，支持用户更新自己的头像和资料
        *   配置媒体文件URL路由，支持头像文件访问
        *   实现前端头像选择、预览、上传的完整流程
        *   添加文件格式验证（JPG/PNG）和大小限制（2MB）

*   **2. 实现用户名修改功能 (`feat`, `backend`, `frontend`)**:
    *   **后端文件**: `backend/accounts/views.py`, `backend/accounts/urls.py`
    *   **前端文件**: `frontend/src/components/AccountSettings.tsx`
    *   **内容**:
        *   新增 `UpdateUsernameAPI` 视图，支持用户名更新
        *   添加用户名唯一性验证和长度限制（3-20字符）
        *   简化前端用户名管理逻辑，统一使用username字段

*   **3. 实现密码修改功能 (`feat`, `backend`, `frontend`)**:
    *   **后端文件**: `backend/accounts/views.py`, `backend/accounts/urls.py`
    *   **前端文件**: `frontend/src/components/SecuritySettings.tsx`
    *   **内容**:
        *   新增 `ChangePasswordAPI` 视图，支持安全的密码修改
        *   实现当前密码验证和新密码强度检查
        *   完善前端密码修改表单和验证逻辑

*   **4. 修复用户Profile自动创建机制 (`fix`, `backend`)**:
    *   **文件**: `backend/accounts/models.py`
    *   **内容**: 添加Django信号处理器，确保新用户注册时自动创建Profile实例，解决头像上传时的关联错误

*   **5. 修复Ant Design消息提示问题 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/App.tsx`, `frontend/src/components/AccountSettings.tsx`, `frontend/src/components/SecuritySettings.tsx`
    *   **内容**:
        *   配置Ant Design App组件包装器
        *   使用App.useApp() hook获取正确的message实例
        *   解决操作成功/失败提示不显示的问题

*   **6. 优化头像显示和实时更新 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/AccountSettings.tsx`, `frontend/src/pages/UserDashboard.tsx`
    *   **内容**:
        *   修复头像URL处理，添加完整域名前缀
        *   实现头像立即预览功能
        *   通过自定义事件实现右上角头像实时更新
        *   解决中文文件名URL编码问题

*   **7. 优化密码确认UI设计 (`ui`, `frontend`)**:
    *   **文件**: `frontend/src/components/SecuritySettings.tsx`
    *   **内容**:
        *   将密码匹配验证图标移至输入框外部显示
        *   实现实时密码匹配检查和视觉反馈
        *   统一所有密码输入框的长度，保持界面一致性
        *   添加绿色勾号/红色叉号的直观提示

*   **8. 修复用户名显示不一致问题 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/pages/UserDashboard.tsx`, `frontend/src/components/AccountSettings.tsx`
    *   **内容**:
        *   简化用户名显示逻辑，统一使用username字段
        *   实现用户信息的动态加载和实时同步
        *   解决登录后显示错误用户名的问题

*   **9. 修复前端API调用token问题 (`fix`, `frontend`)**:
    *   **文件**: `frontend/src/components/AccountSettings.tsx`, `frontend/src/components/SecuritySettings.tsx`, `frontend/src/pages/FaceManagementPage.tsx`
    *   **内容**: 统一token键名为'authToken'，解决API调用认证失败的问题

*   **10. 优化开发配置 (`chore`, `frontend`)**:
    *   **文件**: `frontend/vite.config.ts`
    *   **内容**: 调整Vite代理配置，确保前后端通信正常

**改进效果**:
*   **功能完备性**: 用户现在可以完整地管理个人信息，包括头像、用户名和密码
*   **用户体验**: 所有操作都有即时反馈，头像可立即预览，界面响应迅速
*   **界面一致性**: 统一的输入框样式和验证提示，专业的UI设计
*   **系统稳定性**: 修复了多个关键bug，提升了系统的可靠性和可用性
*   **开发体验**: 完善的错误处理和调试信息，便于后续维护和扩展

---

**V0.6.0 feat(frontend): 重构用户仪表盘并实现多功能管理界面**

日期：2025-07-07

**类型**: `feat`
**范围**: `frontend`

**说明**:
本次更新对用户仪表盘（User Dashboard）进行了一次全面的重构和功能升级。我们将原有的简单欢迎页，改造为一个功能齐全、布局专业的管理后台，显著提升了用户体验和系统的可扩展性。

**实现细节**:
*   **1. 重构仪表盘布局 (`refactor`, `frontend`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/UserDashboard.tsx`
    *   **内容**: 使用 Ant Design 的 `Layout`、`Sider`、`Header` 和 `Menu` 组件，搭建了包含侧边栏导航和顶部用户信息栏的经典后台布局。

*   **2. 新增多功能管理模块 (`feat`, `frontend`)**:
    *   **文件**: `proj_access_control/frontend/src/components/AccountSettings.tsx`, `proj_access_control/frontend/src/components/SecuritySettings.tsx`, `proj_access_control/frontend/src/pages/FaceManagementPage.tsx`
    *   **内容**:
        *   创建了“账户信息”模块，提供修改用户名和头像的表单。
        *   创建了“安全设置”模块，提供修改密码的表单。
        *   创建了“人脸管理”页面，用于上传和注册人脸数据。

*   **3. 实现嵌套路由 (`feat`, `frontend`)**:
    *   **文件**: `proj_access_control/frontend/src/App.tsx`
    *   **内容**: 应用了 React Router 的嵌套路由机制。`/dashboard` 作为父路由渲染整体布局，其子路由（如 `account`, `security`）在内容区动态渲染对应的功能模块，实现了流畅的单页应用体验。

*   **4. 修复用户名显示BUG (`fix`, `frontend`, `backend`)**:
    *   **后端**:
        *   **文件**: `proj_access_control/backend/accounts/views.py`
        *   **内容**: 修改了登录API，使其在响应中返回用户名。
    *   **前端**:
        *   **文件**: `proj_access_control/frontend/src/pages/AuthPage.tsx`, `UserDashboard.tsx`, `AccountSettings.tsx`
        *   **内容**: 在前端实现了用户名的获取、本地存储和动态显示，解决了此前仪表盘显示静态占位符的问题。

**改进效果**:
*   **用户体验提升**: 用户现在拥有一个功能集中、导航清晰的现代化管理界面。
*   **代码结构优化**: 通过模块化和嵌套路由，前端代码变得更加清晰、可维护和可扩展。
*   **功能完备性**: 为用户提供了管理个人信息、安全设置和生物特征（人脸）的核心功能。

---

**V0.5.0 feat(backend): 实现核心人脸识别服务**

日期：2025-07-07

**类型**: `feat`
**范围**: `backend`

**说明**:
本次更新为项目后端引入了核心的人脸识别功能。我们成功实现了从人脸注册到1:1人脸验证的全链路服务，并解决了 `deepface` 库在项目环境中遇到的多个兼容性与性能问题。这为后续实现完整的门禁认证流程（NFC刷卡 + 人脸验证）奠定了坚实的技术基础。

**实现细节**:
*   **1. 实现人脸注册与验证API (`feat`, `backend`)**:
    *   **文件**: `proj_access_control/backend/face_db/views.py`
    *   **内容**: 创建了 `FaceRegistrationAPI`，允许认证用户上传图片，后端服务会提取面部特征向量并存入数据库。

*   **2. 实现核心人脸比对服务 (`refactor`, `backend`)**:
    *   **文件**: `proj_access_control/backend/face_db/services.py`
    *   **内容**: 在遭遇 `deepface.verify` 函数的兼容性问题后，将核心比对逻辑重构为手动计算余弦距离。服务现在使用 `deepface.represent` 提取特征，然后通过 `numpy` 高效地计算两个特征向量的相似度。这个方案被证明是稳定且可靠的。

*   **3. 解决模块导入与缓存问题 (`fix`, `backend`)**:
    *   **文件**: `proj_access_control/backend/face_db/views.py`, `proj_access_control/backend/face_db/services.py`
    *   **内容**: 解决了因 `deepface` 内部模块导入失败和 Django 顽固的模块缓存导致的服务器启动错误。通过重构代码、清理缓存和调整模块导入方式，确保了服务的稳定性。

*   **4. 优化识别阈值 (`feat`, `backend`)**:
    *   **文件**: `proj_access_control/backend/face_db/services.py`
    *   **内容**: 根据实际测试结果，将人脸比对的距离阈值从 `0.40` 放宽至 `0.50`，在保证安全性的前提下提高了识别的容错率，以更好地适应真实场景中的光照、角度变化。

*   **5. 添加测试脚本 (`test`, `backend`)**:
    *   **文件**: `proj_access_control/backend/test_script/`
    *   **内容**: 创建了独立的注册和验证测试脚本，用于端到端地验证整个流程，极大地提高了调试效率和代码质量。

**改进效果**:
*   **核心功能实现**: 项目现在具备了稳定、可靠的人脸识别能力。
*   **技术栈验证**: 成功将 `deepface` 集成到 Django 项目中，并解决了相关的技术难题。
*   **可测试性**: 独立的测试脚本确保了该核心功能的健壮性

---

**V0.4.0 feat(auth): 实现完整的前后端认证与授权流程**

日期：2025-07-07

**类型**: `feat`
**范围**: `auth`

**说明**:
本次更新是一次对认证授权系统的全面升级。我们从前端到后端，完整地实现并修复了用户与管理员的注册、登录、授权、跳转和路由保护全链路逻辑。这标志着应用的核心认证模块已达到功能完备且安全可靠的状态。

**实现细节**:
*   **1. 完善用户注册流程 (`feat`, `frontend`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/AuthPage.tsx`
    *   **内容**: 增强了用户注册表单，新增“确认密码”字段，并加入了密码最小长度和两次输入一致性的前端验证，提升了用户体验和账户安全性。

*   **2. 实现登录后跳转逻辑 (`feat`, `frontend`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/UserDashboard.tsx`, `proj_access_control/frontend/src/pages/AdminDashboard.tsx`, `proj_access_control/frontend/src/App.tsx`
    *   **内容**: 创建了用户和管理员登录成功后跳转的仪表盘（Dashboard）页面，并配置了相应的路由。

*   **3. 实现路由保护机制 (`feat`, `frontend`)**:
    *   **文件**: `proj_access_control/frontend/src/components/ProtectedRoute.tsx`, `proj_access_control/frontend/src/App.tsx`
    *   **内容**: 创建了一个可复用的 `ProtectedRoute` 组件，用于检查本地存储的 Token。应用此组件保护了仪表盘路由，确保只有已登录的用户才能访问相应页面，否则将被重定向至登录页。

*   **4. 修复管理员认证逻辑 (`fix`, `backend`, `frontend`)**:
    *   **后端**:
        *   **文件**: `proj_access_control/backend/accounts/views.py`
        *   **内容**: 修改了 `LoginAPI` 视图，使其在返回的 Token 响应中明确包含 `is_staff` 字段，为前端提供了可靠的管理员身份标识。
    *   **前端**:
        *   **文件**: `proj_access_control/frontend/src/pages/AdminLoginPage.tsx`
        *   **内容**: 修复了管理员登录逻辑，现在会严格检查后端返回的 `is_staff` 字段来确认管理员身份，并增加了加载状态和更明确的反馈信息，解决了之前无法成功跳转的问题。

**改进效果**:
*   **功能完整性**: 提供了从注册到登录，再到访问受限内容的全套认证流程。
*   **安全性提升**: 通过路由保护和后端驱动的权限验证，显著增强了应用的安全性。
*   **代码健壮性**: 前后端职责划分更清晰，认证逻辑更严谨，为未来扩展打下了坚实基础。

---

**V0.3.2 fix(frontend): 完善认证流程UI与网页基础配置**

日期：2025-07-07

**类型**: `fix`
**范围**: `frontend`

**说明**:
本次更新是一次全面的UI和UX修复与完善。我们根据用户反馈，对认证流程的视觉风格和交互便利性进行了精细化调整，并对网页的基础信息（如标题、图标和语言）进行了标准化配置，显著提升了应用的专业性和用户体验。

**实现细节**:
*   **1. 统一认证页面视觉风格 (`ui`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/AuthPage.css`, `proj_access_control/frontend/src/pages/AdminLoginPage.tsx`
    *   **内容**: 将主认证页面和管理员登录页面的背景统一为现代化的橙白渐变色，确保了视觉风格的一致性。

*   **2. 优化认证流程交互 (`feat`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/AuthPage.tsx`, `proj_access_control/frontend/src/pages/AdminLoginPage.tsx`
    *   **内容**: 在主认证页面添加了样式统一的“管理员登录”链接；在管理员登录页面添加了“返回用户登录”的链接，打通了不同认证角色间的导航，使流程更顺畅。

*   **3. 标准化网页基础配置 (`chore`)**:
    *   **文件**: `proj_access_control/frontend/index.html`, `proj_access_control/frontend/public/ICON.png`
    *   **内容**:
        *   将 `index.html` 的语言标识设置为 `zh-CN`。
        *   将网页标题更新为“智能门禁管理系统”。
        *   添加了项目图标 `ICON.png` 并配置为网站的 Favicon。

**改进效果**:
*   **视觉与品牌统一**: 应用的所有面向用户的初始页面现在拥有了一致的、专业的设计风格。
*   **交互更直观**: 用户可以轻松地在不同认证页面间导航，无需记忆或手动输入URL。
*   **专业性提升**: 正确的网页标题、图标和语言配置，提升了应用的整体专业形象。

---

**V0.3.1 ui(frontend): 全面优化认证页面的视觉风格与交互流程**

日期：2025-07-07

**类型**: `ui`
**范围**: `frontend`

**说明**:
本次更新根据用户反馈，对整个认证流程（包括用户和管理员）的 UI 和 UX 进行了全面的优化和统一。调整内容涵盖了背景样式、组件布局和交互链接，旨在提供一个视觉上更专业、风格更统一、操作上更便捷的认证体验。

**实现细节**:
*   **1. 统一页面背景 (`ui`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/AuthPage.css`, `proj_access_control/frontend/src/pages/AdminLoginPage.tsx`
    *   **内容**: 将主认证页面和管理员登录页面的背景统一为现代化的橙白渐变色，确保了不同认证角色入口的视觉连贯性。

*   **2. 优化管理员登录入口 (`ui`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/AuthPage.css`, `proj_access_control/frontend/src/pages/AuthPage.tsx`
    *   **内容**: 调整了主认证页面上“管理员登录”链接的样式，使其颜色更柔和、尺寸更小且无下划线，更好地融入了整体设计。

*   **3. 增强管理员页面交互 (`feat`)**:
    *   **文件**: `proj_access_control/frontend/src/pages/AdminLoginPage.tsx`
    *   **内容**: 在管理员登录页面新增了一个“返回用户登录”的链接，方便用户在两个认证流程之间轻松切换，提升了整体的用户体验。

**改进效果**:
*   **视觉风格统一**: 所有认证相关页面的观感现已保持一致，提升了应用的专业性和品牌感。
*   **交互流程更顺畅**: 通过在不同页面间提供清晰的导航链接，简化了用户的操作路径。
*   **设计更精细**: 对链接等小元素的样式微调，使整体 UI 更加和谐、精致。

---

**V0.3.0 feat(frontend): 初始化前端项目并实现统一的用户认证页面**

日期：2025-07-07

**类型**: `feat`
**范围**: `frontend`

**说明**:
本次更新完成了前端应用的从零到一的搭建。我们使用 `Vite` + `React` + `TypeScript` 技术栈初始化了整个前端项目，并成功构建了一个功能完备、视觉统一的用户认证页面（`AuthPage.tsx`）。该页面整合了登录与注册功能，并实现了用户反馈的特定双栏布局和动画效果，为后续开发奠定了坚实的前端基础。

**实现细节**:
*   **1. 前端项目初始化 (`chore`)**:
    *   **文件**: `proj_access_control/frontend/`
    *   **内容**: 使用 `npm create vite@latest` 命令创建了标准的前端项目结构，包含了 `Vite`、`TypeScript` 和 `ESLint` 的所有必要配置文件（如 `vite.config.ts`, `tsconfig.json`, `package.json` 等）。

*   **2. 核心依赖集成 (`chore`)**:
    *   **文件**: `proj_access_control/frontend/package.json`
    *   **内容**: 添加并配置了项目核心依赖库：`antd` 用于 UI 组件，`axios` 用于与后端 API 通信，`react-router-dom` 用于前端路由管理。

*   **3. 统一认证页面的实现 (`feat`)**:
    *   **文件**: `frontend/src/pages/AuthPage.tsx`, `frontend/src/pages/AuthPage.css`
    *   **内容**: 创建了集登录和注册功能于一体的 `AuthPage` 组件。使用 CSS 实现了用户要求的、带有动画效果的双栏布局，并通过 `useState` 管理面板的切换状态。

*   **4. 全局主题与样式配置 (`ui`)**:
    *   **文件**: `frontend/src/App.tsx`, `frontend/src/index.css`
    *   **内容**: 在 `App.tsx` 中使用 Ant Design 的 `ConfigProvider` 配置了全局的橙色主题。同时，通过修改 `index.css` 解决了页面垂直居中的布局问题。

*   **5. 认证页面 UI 微调 (`ui`)**:
    *   **文件**: `frontend/src/pages/AuthPage.tsx`
    *   **内容**: 根据最终的用户反馈，增大了页面主标题的字号并调整了其位置，同时将所有按钮和输入框的尺寸统一设置为 `large`，优化了最终的视觉效果和用户体验。

**改进效果**:
*   **功能完备的前端基础**: 搭建了可扩展、可维护的前端项目框架。
*   **现代化的用户界面**: 实现了一个美观、交互流畅的统一认证入口。
*   **组件化开发**: `AuthPage` 的实现遵循了组件化的开发思想，易于理解和后续修改。

---

**V0.2.0 feat(backend): 实现NFC+人脸双重验证的核心后端逻辑**

日期：2025-07-07

**类型**: `feat`
**范围**: `backend`

**说明**:
在 V0.1.0 的基础上，本次更新根据新的业务需求，对系统架构和核心功能进行了重大升级。引入了 NFC 卡作为第一道验证，将门禁流程优化为“NFC卡查找用户 + 1:1人脸验证”，并成功解决了所有棘手的环境依赖问题，为项目后续开发奠定了稳定可靠的基础。

**实现细节**:
*   **1. 架构升级与文档同步 (`docs`)**:
    *   **文件**: `proj_access_control/docs/PROJECT_PLAN.md`
    *   **内容**: 更新了项目计划文档，明确了“NFC + 人脸”双重验证的业务流程，并更新了数据模型设计。

*   **2. 依赖问题修复 (`fix`)**:
    *   **文件**: `proj_access_control/backend/requirements.txt`
    *   **内容**: 经过多次尝试，最终确定了一套能在用户环境中稳定工作的依赖组合，详情查看backend/requirements.txt文件。

*   **3. 数据模型扩展 (`feat`)**:
    *   **文件**: `accounts/models.py`
    *   **内容**: 为 `Profile` 模型添加了 `nfc_card_id` 字段，用于存储与用户绑定的唯一 NFC 卡号，并成功应用了数据库迁移。

*   **4. NFC 管理 API (`feat`)**:
    *   **文件**: `accounts/serializers.py`, `accounts/views.py`, `accounts/urls.py`
    *   **内容**: 扩展了用户管理的序列化器和视图，新增了 `api/users/<int:pk>/profile/` API 端点，允许管理员为指定用户分配或更新 NFC 卡号。

**改进效果**:
*   **业务逻辑升级**: 系统核心逻辑从简单的人脸识别升级为更安全、更高效的“NFC + 人脸”双重验证。
*   **环境稳定性**: 彻底解决了困扰项目进展的 Python 环境和依赖库版本冲突问题。
*   **功能完备性**: 后端现在完全支持新方案所需的数据管理和 API 接口，为后续开发做好了充分准备。

---

**V0.1.0 feat(backend): 初始化后端项目并实现核心功能**

日期：2025-07-07

**类型**: `feat`
**范围**: `backend`

**说明**:
本次提交完成了门禁系统后端的从零到一的搭建。工作内容涵盖了项目规划、基础结构创建、数据库建模、用户认证系统、集成AI的人脸注册API，以及用于硬件通信的WebSocket服务。这为后续的前端开发和硬件联调提供了完整且功能完备的后端基础。

**实现细节**:
*   **1. 项目架构与规划 (`docs`)**:
    *   **文件**: `proj_access_control/docs/PROJECT_PLAN.md`
    *   **内容**: 创建了详细的项目计划文档，明确了基于 Django 和 React 的技术栈，并最终确定了使用 WebSocket 与 ESP32 进行双向通信的系统架构。

*   **2. 项目基础结构与依赖 (`chore`)**:
    *   **文件**: `proj_access_control/backend/`
    *   **内容**: 创建了完整的 Django 项目结构，包括一个 `config` 项目和 `accounts`, `face_db`, `access_control` 三个应用。
    *   **文件**: `proj_access_control/requirements.txt`
    *   **内容**: 定义了项目所需的核心依赖，如 `Django`, `djangorestframework`, `channels`, `deepface`, `opencv-python`。
    *   **文件**: `proj_access_control/.gitignore`, `proj_access_control/backend/.gitignore`
    *   **内容**: 添加了标准的 `.gitignore` 文件以排除虚拟环境、数据库、缓存和IDE配置文件。

*   **3. 数据库模型定义 (`feat`)**:
    *   **文件**: `accounts/models.py`, `face_db/models.py`, `access_control/models.py`
    *   **内容**: 定义了 `Profile`, `FaceData`, `AccessLog` 三个核心数据模型，并成功执行了数据库迁移，创建了相应的表结构。

*   **4. 用户认证 API (`feat`)**:
    *   **文件**: `accounts/serializers.py`, `accounts/views.py`, `accounts/urls.py`
    *   **内容**: 实现了基于 DRF Token Authentication 的用户注册、登录和信息获取 API。

*   **5. 人脸注册 API (`feat`)**:
    *   **文件**: `face_db/services.py`, `face_db/serializers.py`, `face_db/views.py`, `face_db/urls.py`
    *   **内容**: 实现了人脸注册的核心功能。`services.py` 封装了调用 `DeepFace` 库从图片中提取特征向量的逻辑。API 接收 Base64 编码的图片，处理后将特征向量存入数据库。

*   **6. 硬件通信 WebSocket 服务 (`feat`)**:
    *   **文件**: `access_control/consumers.py`, `access_control/routing.py`, `config/asgi.py`
    *   **内容**: 使用 `Django Channels` 创建了一个专用于硬件（ESP32）连接的 WebSocket 端点 (`/ws/device/{device_id}/`)。`DeviceConsumer` 负责处理连接、断开和消息接收，为后续的实时双向通信打下基础。

**改进效果**:
*   **功能完备的后端**: 提供了项目所需的所有核心后端服务，为前端开发做好了充分准备。
*   **清晰的架构**: 项目结构清晰，模块化，易于维护和扩展。
*   **技术验证**: 成功集成了 `deepface` 和 `channels`，验证了核心技术方案的可行性。

---
