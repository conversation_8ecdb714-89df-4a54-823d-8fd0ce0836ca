# 智能门禁与人脸识别系统

> 一个基于 Django、React 和 ESP32 构建的，集成了 NFC 刷卡与人脸识别双重验证的智能门禁系统。

本项目是一个全栈的课程设计，旨在演示如何将 Web 技术、物联网硬件和人工智能（人脸识别）结合起来，构建一个真实世界的应用程序。

## ✨ 项目简介

该系统允许用户通过 **NFC 刷卡** + **人脸识别** 的双重验证方式来开门。管理员可以通过一个功能丰富的 Web 界面实时监控设备状态、管理用户和设备、并审查所有访问日志。

-   **核心流程**: 用户刷卡后，系统识别其身份，并触发摄像头进行 1:1 人脸比对。只有在人脸比对成功后，才会向硬件发送开门指令。
-   **实时性**: 前后端、后端与硬件之间均采用 WebSocket 进行实时双向通信。

## 🏛️ 项目架构

系统采用前后端分离的架构，并通过 WebSocket 与硬件设备进行通信。

![门禁系统架构图](./docs/pic/门禁系统架构图.drawio.png)

-   **前端 (Frontend)**: 用户和管理员的交互界面。
-   **后端 (Backend)**: 处理所有业务逻辑、API 请求和实时通信的核心。
-   **硬件 (ESP32)**: 负责物理世界的交互，如读取 NFC 卡和控制门锁。

## 🛠️ 技术栈

-   **后端**: Django, Django REST Framework, Django Channels, Deepface
-   **前端**: React, Vite, TypeScript, Ant Design
-   **硬件**: ESP32 (Arduino C++), MFRC522 (NFC)
-   **数据库**: SQLite (开发环境), PostgreSQL (生产环境推荐)
-   **实时通信**: WebSocket

## 📁 目录结构

项目主要由以下几个部分组成，每个部分都有其独立的 `README.md` 文件提供更详细的说明。

-   [`backend/`](./backend/)
    -   Django 后端项目。负责所有 API、WebSocket 服务和业务逻辑。
    -   **[➡️ 查看后端详细文档](./backend/README.md)**

-   [`frontend/`](./frontend/)
    -   React 前端项目。为用户和管理员提供 Web 界面。
    -   **[➡️ 查看前端详细文档](./frontend/README.md)**

-   [`esp32_arduino/`](./esp32_arduino/)
    -   ESP32 硬件的固件代码。
    -   **[➡️ 查看硬件详细文档](./esp32_arduino/README.md)**

-   [`docs/`](./docs/)
    -   包含项目的设计文档、规划、审查报告和架构图等。
    -   **[➡️ 查看项目规划文档](./docs/PROJECT_PLAN.md)**
    -   **[➡️ 查看最新审查报告](./docs/REVIEW_REPORT.md)**

## 🚀 快速上手

要完整地运行整个系统，你需要分别设置和启动后端、前端，并为硬件刷写固件。

1.  **启动后端服务**:
    -   详情请参考 [`backend/README.md`](./backend/README.md)。

2.  **启动前端开发服务器**:
    -   详情请参考 [`frontend/README.md`](./frontend/README.md)。

3.  **刷写硬件固件**:
    -   详情请参考 [`esp32_arduino/README.md`](./esp32_arduino/README.md)。

## 📝 项目当前状态

**“功能完善，蓄势待发”**

目前，项目的所有核心功能均已开发完成。前后端基础框架、硬件固件以及它们之间的通信链路已经完全打通并可正常工作。

-   **双重验证**: 后端已实现NFC刷卡后的二次人脸识别验证逻辑。
-   **实时监控**: 前端管理员界面已实现动态、可选择的实时视频流监控功能。

详细的分析和项目历史，请查阅最新的 **[项目审查报告](./docs/REVIEW_REPORT.md)**。