# 项目计划书：门禁系统上位机

## 1. 概述

本项目旨在开发一个基于人脸识别的门禁系统上位机。该系统前端采用 React + TypeScript，后端采用 Django + Python，并集成 Deepface 库进行人脸识别。系统将通过 Wi-Fi 与 ESP32-S3 单片机进行双向 WebSocket 通信，实现人脸识别开门与 NFC 刷卡信息上报等功能。项目定位为课程设计，复杂度适中。

## 2. 整体架构

采用前后端分离架构，通过 WebSocket 实现实时数据通信。

![架构图](./pic/门禁系统架构图.drawio.png)

```mermaid
graph TD
    subgraph "用户设备"
        A["浏览器/客户端"]
    end

    subgraph "外部硬件"
        B[USB 摄像头]
        C["ESP32-S3 单片机 (Wi-Fi)"]
    end

    subgraph "上位机系统 (服务器)"
        D{React 前端}
        E{"Django 后端 (Channels)"}
        F[(SQLite 数据库)]
        G[Deepface 服务]
    end

    A -- "HTTP/S & WebSocket" --> D
    D -- REST API --> E
    B -- OpenCV读取 --> E
    C -- "WebSocket (双向)" --> E
    E -- Python调用 --> G
    E -- ORM --> F

```

## 3. 技术选型

-   **前端:**
    -   框架: `React` + `TypeScript`
    -   UI库: `Ant Design`
    -   数据请求: `Axios`
    -   实时通信: 原生 `WebSocket` API 或 `socket.io-client`
-   **后端:**
    -   框架: `Django` + `Django REST Framework`
    -   数据库: `SQLite` (Django默认)
    -   实时通信: `Django Channels`
    -   人脸识别: `deepface`
    -   与硬件通信: `Django Channels` (WebSocket)
    -   摄像头处理: `OpenCV-Python`

## 4. 模块与功能详细设计

### 后端 (Django)

1.  **`accounts` 应用 (用户与权限):**
    -   **模型:** 使用 Django 内置 `User` 模型。关联一个 `Profile` 模型，用于存放用户的姓名、头像以及**唯一的 NFC 卡号**。
    -   **API:** 提供用户注册、登录、信息获取与更新的接口。管理员有权为用户分配或更新 NFC 卡号。

2.  **`face_db` 应用 (人脸数据库):**
    -   **模型:** `FaceData` (关联到 `User`，存储注册时提取并**向量化**的人脸特征 `embedding`)。
    -   **API:** 提供用户为自己注册人脸的接口。在注册时，后端会调用 `DeepFace.represent()` 将人脸图像转换为特征向量并存储，以加速后续比对。

3.  **`access_control` 应用 (门禁核心):**
    -   **模型:** `AccessLog` (记录门禁事件)。
    -   **核心逻辑 (双重验证流程):**
        1.  **等待NFC刷卡:** 系统通过 WebSocket 等待 ESP32 上报 NFC 刷卡事件，事件中包含 `card_id`。
        2.  **用户查找:** 后端根据 `card_id` 查找对应的用户。如果找不到，则流程结束。
        3.  **调用摄像头服务:** 系统通过HTTP请求外部的摄像头服务，捕获一张实时照片。
        4.  **人脸验证 (1:1):** 查找到用户后，系统进入人脸验证阶段。它会将实时捕获的照片与**该特定用户**已注册的脸部特征向量进行 **1:1 比对**（使用 `DeepFace.verify()`）。
        5.  **授权开门:** 如果人脸验证成功，后端通过 WebSocket 向 ESP32 发送开门指令。
        6.  **日志记录:** 无论成功与否，整个尝试过程（包括NFC信息和人脸抓拍）都会被记录到日志中。
    -   **API:** 提供查询门禁日志的接口（区分用户和管理员权限）。

### 前端 (React)

1.  **登录页:** 用户登录。
2.  **管理员视图:**
    -   **主控台:** 可选择的实时视频流、实时门禁日志。
    -   **用户管理页:** 管理所有用户。
    -   **人脸库页:** 查看所有人脸数据。
3.  **普通用户视图:**
    -   **个人中心:** 修改个人信息、注册/更新人脸、查看个人门禁记录。

## 5. 数据库设计 (Django Models)

```python
# accounts/models.py
from django.contrib.auth.models import User
from django.db import models

class Profile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    full_name = models.CharField(max_length=100, blank=True)
    avatar = models.ImageField(upload_to='avatars/', null=True, blank=True)
    nfc_card_id = models.CharField(max_length=50, unique=True, null=True, blank=True, help_text="Unique ID of the user's NFC card")

# face_db/models.py
class FaceData(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    embedding = models.BinaryField()
    image_path = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

# access_control/models.py
class AccessLog(models.Model):
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    is_success = models.BooleanField()
    snapshot_path = models.CharField(max_length=255)
```

## 6. 开发路线图

-   **第一阶段：后端基础搭建 (已完成)**
    1.  创建 Django 项目和应用。
    2.  定义数据模型并迁移数据库。
    3.  实现用户认证 API。
    4.  配置 `Django Channels`。

-   **第二阶段：核心功能后端实现 (已完成)**
    1.  封装 `deepface` 服务。
    2.  实现人脸管理 API。
    3.  在 Django Channels 中为硬件设备实现专用的 WebSocket Consumer。
    4.  实现 WebSocket 核心控制逻辑。

-   **第三阶段：前端开发与联调 (已完成)**
    1.  创建 React 项目并搭建路由。
    2.  开发登录页及主功能页面。
    3.  对接 WebSocket 实现实时功能。
    4.  完成前后端功能联调。

-   **第四阶段：整合、测试与文档 (已完成)**
    1.  端到端功能测试。
    2.  Bug 修复与体验优化。
    3.  编写项目说明文档。