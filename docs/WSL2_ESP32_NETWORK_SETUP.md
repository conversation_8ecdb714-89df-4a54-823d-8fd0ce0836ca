# WSL2与ESP32设备网络连接配置指南

## 1. 问题背景

本项目后端服务运行在Windows Subsystem for Linux 2 (WSL2) 环境中。WSL2使用一个独立的虚拟网络，其IP地址与宿主Windows系统的IP地址不同，并且默认情况下，外部物理网络中的设备（如ESP32）无法直接访问在WSL2内部运行的服务。

本文档旨在说明如何配置网络，以允许在局域网中的ESP32设备能够成功连接到运行在WSL2中的Django后端WebSocket服务。

## 2. 核心原理

解决方案的核心是利用Windows宿主机的**端口转发（Port Forwarding）**功能。我们将Windows主机的某个端口（例如：8000）收到的所有网络请求，全部转发给WSL2虚拟机内部对应的端口。

这样，ESP32设备在连接时，只需要知道Windows主机的局域网IP地址即可，由Windows负责将请求“桥接”到WSL2。

**数据流:**
`ESP32` -> `Windows主机IP:8000` -> `[端口转发]` -> `WSL2内部IP:8000` -> `Django服务`

## 3. 配置步骤

---

### 步骤一：获取WSL2的IP地址

WSL2的虚拟IP地址在每次重启后可能会发生变化，因此在每次启动服务前都需要获取最新的IP。

在Windows的终端（CMD或PowerShell）中运行以下命令：

```bash
wsl hostname -I
```

该命令会输出WSL2当前的IP地址，例如 `**************`。请记下这个IP地址。

---

### 步骤二：在Windows上设置端口转发

此步骤需要在**管理员权限**的PowerShell中执行。

1.  打开开始菜单，搜索“PowerShell”。
2.  右键点击“Windows PowerShell”，选择“以管理员身份运行”。
3.  在管理员PowerShell窗口中，执行以下命令来设置端口转发规则。请将命令中的`<WSL2_IP>`替换为上一步获取到的真实IP地址。

    ```powershell
    # 命令格式: netsh interface portproxy add v4tov4 listenport=[监听端口] listenaddress=0.0.0.0 connectport=[目标端口] connectaddress=[WSL2_IP]
    
    # 为本项目设置端口8000的转发
    netsh interface portproxy add v4tov4 listenport=8000 listenaddress=0.0.0.0 connectport=8000 connectaddress=<WSL2_IP>
    ```

    **示例**:
    如果你的WSL2 IP是 `**************`，那么命令就是：
    ```powershell
    netsh interface portproxy add v4tov4 listenport=8000 listenaddress=0.0.0.0 connectport=8000 connectaddress=**************
    ```

4.  **验证转发规则** (可选):
    你可以使用以下命令查看当前所有的端口转发规则：
    ```powershell
    netsh interface portproxy show all
    ```

---

### 步骤三：配置ESP32

在ESP32的固件代码中，你需要将WebSocket服务器的地址指向**Windows主机的局域网IP地址**。

1.  获取Windows主机的IP地址。在CMD或PowerShell中运行 `ipconfig`，查找你的以太网或Wi-Fi适配器的IPv4地址（例如 `*************`）。
2.  修改ESP32的配置文件（例如 `esp32_arduino/config_template.h`），设置`WEBSOCKET_HOST`。

    ```cpp
    // ...
    const char* WEBSOCKET_HOST = "*************"; // <-- 修改为你的Windows主机IP
    const int WEBSOCKET_PORT = 8000;
    // ...
    ```

---

### 步骤四：配置Windows防火墙

在大多数情况下，你需要为监听的端口（本例中为8000）创建一个入站防火墙规则，以允许来自局域网的连接。

1.  打开“高级安全Windows Defender防火墙”。
2.  选择“入站规则”，然后点击右侧的“新建规则...”。
3.  选择“端口”，点击“下一步”。
4.  选择“TCP”，并指定特定本地端口为 `8000`，点击“下一步”。
5.  选择“允许连接”，点击“下一步”。
6.  根据你的网络环境勾选规则应用的范围（通常“域”和“专用”即可），点击“下一步”。
7.  为规则命名，例如 `WSL2 Port 8000 Forward`，然后点击“完成”。

---

## 4. 日常开发流程

1.  在WSL2中启动Django后端服务 (`python manage.py runserver 0.0.0.0:8000`)。
2.  打开Windows终端，运行 `wsl hostname -I` 获取WSL2的IP。
3.  打开一个**管理员PowerShell**，运行 `netsh` 端口转发命令。
    *   **注意**: 如果上次的IP没有变化，无需重复设置。如果IP变了，需要先删除旧规则再添加新规则：
        ```powershell
        # 删除旧规则
        netsh interface portproxy delete v4tov4 listenport=8000 listenaddress=0.0.0.0
        # 添加新规则
        netsh interface portproxy add v4tov4 listenport=8000 listenaddress=0.0.0.0 connectport=8000 connectaddress=<NEW_WSL2_IP>
        ```
4.  确认ESP32已配置为连接Windows主机的IP。
5.  启动ESP32设备，它现在应该能够成功连接到后端服务了。
