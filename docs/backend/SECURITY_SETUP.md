# 🔐 安全配置指南

本指南将帮你正确配置智能门禁系统的安全设置，确保生产环境的安全性。

## 📋 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

#### 开发环境
```bash
# 复制开发环境配置（已包含安全的开发密钥）
cp .env.example .env
```

#### 生产环境
```bash
# 复制生产环境模板
cp .env.production.example .env.production

# 编辑配置文件
nano .env.production
```

### 3. 生成安全密钥

为生产环境生成新的SECRET_KEY：

```bash
python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
```

将生成的密钥复制到 `.env.production` 文件中的 `DJANGO_SECRET_KEY`。

### 4. 运行安全检查

```bash
python check_security.py
```

## 🔧 详细配置说明

### 环境变量配置

| 变量名 | 开发环境 | 生产环境 | 说明 |
|--------|----------|----------|------|
| `DJANGO_SECRET_KEY` | 开发密钥 | **必须更改** | Django加密密钥 |
| `DJANGO_DEBUG` | `True` | `False` | 调试模式 |
| `DJANGO_ALLOWED_HOSTS` | `localhost,127.0.0.1` | 你的域名 | 允许的主机 |

### 生产环境必做事项

#### ✅ 必须完成
- [ ] 生成并设置新的 `DJANGO_SECRET_KEY`
- [ ] 设置 `DJANGO_DEBUG=False`
- [ ] 配置正确的 `DJANGO_ALLOWED_HOSTS`
- [ ] 配置生产数据库（PostgreSQL推荐）
- [ ] 启用HTTPS和安全头

#### 🔒 安全增强
- [ ] 配置防火墙
- [ ] 设置定期备份
- [ ] 配置日志监控
- [ ] 启用访问日志

## 🚀 部署检查清单

### 部署前检查
```bash
# 1. 运行安全检查
python check_security.py

# 2. 运行Django安全检查
python manage.py check --deploy

# 3. 收集静态文件
python manage.py collectstatic

# 4. 运行数据库迁移
python manage.py migrate
```

### 生产环境启动
```bash
# 使用生产环境配置
export DJANGO_SETTINGS_MODULE=config.settings
export $(cat .env.production | xargs)

# 启动服务
gunicorn config.wsgi:application --bind 0.0.0.0:8000
```

## 🛡️ 安全最佳实践

### 1. 密钥管理
- 永远不要在代码中硬编码密钥
- 定期轮换SECRET_KEY
- 使用环境变量或密钥管理服务

### 2. 数据库安全
- 使用强密码
- 限制数据库访问权限
- 定期备份数据

### 3. 网络安全
- 使用HTTPS
- 配置防火墙
- 限制不必要的端口访问

### 4. 监控和日志
- 启用访问日志
- 监控异常登录
- 设置安全告警

## 🔍 故障排除

### 常见问题

#### SECRET_KEY错误
```
django.core.exceptions.ImproperlyConfigured: The SECRET_KEY setting must not be empty.
```
**解决方案**: 确保 `.env` 文件存在且包含 `DJANGO_SECRET_KEY`

#### ALLOWED_HOSTS错误
```
DisallowedHost at /
Invalid HTTP_HOST header
```
**解决方案**: 在 `DJANGO_ALLOWED_HOSTS` 中添加你的域名

#### 静态文件404
**解决方案**: 运行 `python manage.py collectstatic`

## 📞 获取帮助

如果遇到安全配置问题：

1. 运行 `python check_security.py` 获取详细检查报告
2. 查看Django官方安全文档
3. 检查日志文件 `logs/django.log`

## 🔄 更新和维护

### 定期任务
- [ ] 更新依赖包
- [ ] 检查安全漏洞
- [ ] 备份数据库
- [ ] 监控系统性能

### 安全更新
```bash
# 检查过时的包
pip list --outdated

# 更新安全相关包
pip install --upgrade django djangorestframework

# 重新运行安全检查
python check_security.py
```
