# 🔒 HTTPS 和 WebSocket 安全配置指南

本指南将帮助你为智能门禁系统配置HTTPS和安全的WebSocket连接。

## 📋 快速开始

### 1. 开发环境 (HTTP)

开发环境默认使用HTTP，无需额外配置：

```bash
# 后端
cd backend
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000

# 前端
cd frontend
npm run dev
```

访问地址：
- 前端: http://localhost:5173
- 后端API: http://127.0.0.1:8000
- WebSocket: ws://127.0.0.1:8000/ws/

### 2. 生产环境 (HTTPS)

生产环境需要配置HTTPS证书和安全设置。

## 🔧 HTTPS 配置方法

### 方法一：使用 Let's Encrypt (推荐)

```bash
# 安装 Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 方法二：使用自签名证书 (仅开发测试)

```bash
# 创建证书目录
mkdir -p certs

# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout certs/localhost-key.pem -out certs/localhost.pem -days 365 -nodes -subj "/CN=localhost"

# 设置环境变量
echo "USE_HTTPS=True" >> .env
```

### 方法三：使用反向代理 (Nginx)

创建 Nginx 配置文件：

```nginx
# /etc/nginx/sites-available/access_control
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /path/to/your/certificate.pem;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # 静态文件
    location /static/ {
        alias /var/www/access_control/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /media/ {
        alias /var/www/access_control/media/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # API 请求
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # WebSocket 连接
    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 前端应用
    location / {
        proxy_pass http://127.0.0.1:5173;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/access_control /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔐 WebSocket 安全配置

### 1. 用户认证

前端连接示例：
```javascript
// 获取用户token
const token = localStorage.getItem('authToken');

// 建立WebSocket连接
const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
const wsUrl = `${protocol}//${window.location.host}/ws/user/dashboard/?token=${token}`;
const socket = new WebSocket(wsUrl);

socket.onopen = function(event) {
    console.log('WebSocket connected');
};

socket.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};
```

### 2. 设备认证

ESP32设备连接示例：
```cpp
#include <WebSocketsClient.h>

WebSocketsClient webSocket;

void setup() {
    // WiFi连接代码...
    
    // WebSocket连接
    String deviceKey = "device_esp32_001_secret_key";
    String wsPath = "/ws/device/esp32_001/?device_key=" + deviceKey;
    
    webSocket.begin("your-domain.com", 443, wsPath, "wss");
    webSocket.onEvent(webSocketEvent);
    webSocket.setReconnectInterval(5000);
}

void webSocketEvent(WStype_t type, uint8_t * payload, size_t length) {
    switch(type) {
        case WStype_CONNECTED:
            Serial.println("WebSocket Connected");
            break;
        case WStype_TEXT:
            Serial.printf("Received: %s\n", payload);
            break;
        case WStype_DISCONNECTED:
            Serial.println("WebSocket Disconnected");
            break;
    }
}
```

## 🛡️ 安全最佳实践

### 1. CORS 配置

```python
# settings.py
CORS_ALLOWED_ORIGINS = [
    "https://your-domain.com",
    "https://www.your-domain.com",
    "https://app.your-domain.com",
]

# 生产环境禁用
CORS_ALLOW_ALL_ORIGINS = False
```

### 2. 安全头配置

```python
# 自动启用的安全头 (当 USE_HTTPS=True 时)
SECURE_SSL_REDIRECT = True
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True
```

### 3. WebSocket 认证

```python
# 在 WebSocket 消费者中验证
async def connect(self):
    # 检查用户认证
    user = self.scope.get('user')
    if not user or user.is_anonymous:
        await self.close(code=4003)  # Forbidden
        return
    
    # 检查设备认证
    if self.scope.get('is_device'):
        if not self.scope.get('device_authenticated'):
            await self.close(code=4001)  # Unauthorized
            return
```

## 🔍 测试和验证

### 1. SSL 证书测试

```bash
# 检查证书有效性
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# 在线SSL测试
# 访问: https://www.ssllabs.com/ssltest/
```

### 2. WebSocket 连接测试

```bash
# 测试WebSocket连接
wscat -c "wss://your-domain.com/ws/device/test/?device_key=test_key"
```

### 3. 安全头检查

```bash
# 检查安全头
curl -I https://your-domain.com/api/

# 应该看到:
# Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
# X-Content-Type-Options: nosniff
# X-Frame-Options: DENY
```

## 🚨 故障排除

### 常见问题

1. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认Nginx配置正确
   - 验证SSL证书有效性

2. **CORS 错误**
   - 检查 `CORS_ALLOWED_ORIGINS` 配置
   - 确认前端域名正确
   - 验证请求头设置

3. **SSL 证书问题**
   - 检查证书过期时间
   - 验证域名匹配
   - 确认证书链完整

### 调试命令

```bash
# 检查端口占用
sudo netstat -tlnp | grep :443

# 查看Nginx错误日志
sudo tail -f /var/log/nginx/error.log

# 查看Django日志
tail -f logs/django.log

# 测试WebSocket
python manage.py shell
>>> from channels.testing import WebsocketCommunicator
>>> # 测试代码...
```

## 📚 相关文档

- [Django HTTPS 配置](https://docs.djangoproject.com/en/4.2/topics/security/#ssl-https)
- [Django Channels 安全](https://channels.readthedocs.io/en/stable/topics/security.html)
- [Let's Encrypt 文档](https://letsencrypt.org/docs/)
- [Nginx SSL 配置](https://nginx.org/en/docs/http/configuring_https_servers.html)
