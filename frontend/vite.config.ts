import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 根据当前工作目录中的 `mode` 加载 .env 文件
  // process.cwd() 返回项目根目录
  const env = loadEnv(mode, process.cwd(), '');

  const backend_target = env.VITE_BACKEND_TARGET || 'http://127.0.0.1:8000';
  const ws_target = env.VITE_WS_TARGET || 'ws://127.0.0.1:8000';

  return {
    plugins: [react()],
    server: {
      host: '0.0.0.0', // 允许外部连接
      port: 5173, // 监听的端口号
      strictPort: false, // 如果端口被占用，自动尝试下一个端口
      open: false, // 不自动打开浏览器
      proxy: {
        // 代理 API 请求到 Django 后端
        '/api': {
          target: backend_target,
          changeOrigin: true,
          secure: false,
        },
        // 代理 WebSocket 连接
        '/ws': {
          target: ws_target,
          ws: true,
          changeOrigin: true,
          secure: false,
        },
        // 代理媒体文件
        '/media': {
          target: backend_target,
          changeOrigin: true,
          secure: false,
        },
      },
    },
    // 构建配置
    build: {
      outDir: 'dist',
      sourcemap: true,
      // 优化生产环境
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            antd: ['antd'],
          },
        },
      },
    },
  }
})
