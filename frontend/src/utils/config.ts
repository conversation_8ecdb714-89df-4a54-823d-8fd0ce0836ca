// src/utils/config.ts

/**
 * API基础URL配置
 *
 * - 在开发环境中 (development)，它会指向Vite代理的目标地址 (例如 'http://127.0.0.1:8000')
 *   这样可以利用Vite的代理功能来避免CORS问题。
 *
 * - 在生产环境中 (production)，它会指向当前页面的域名，因为前端和后端通常部署在同一个域名下。
 *   例如，如果前端页面是 https://example.com，那么API请求就会发往 https://example.com/api/...
 */
export const API_BASE_URL = import.meta.env.DEV ? '' : '';

/**
 * 构建完整的媒体文件URL
 * @param relativePath - 从后端API获取的相对路径 (例如 /media/avatars/user.jpg)
 * @returns - 可在<img>或<Avatar>组件中直接使用的完整URL
 */
export const getFullMediaUrl = (relativePath: string | null | undefined): string | undefined => {
    if (!relativePath) {
        return undefined;
    }

    let url = relativePath;

    // 检查并移除硬编码的本地开发服务器地址，这使得代码对后端返回的URL格式更具弹性
    if (url.startsWith('http://127.0.0.1:8000')) {
        url = url.substring('http://127.0.0.1:8000'.length);
    } else if (url.startsWith('http://localhost:8000')) {
        url = url.substring('http://localhost:8000'.length);
    }

    // 如果处理后已经是完整的URL（例如，生产环境的CDN地址），则直接返回
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }

    // 确保路径以'/'开头，然后与API基础URL拼接
    const formattedPath = url.startsWith('/') ? url : `/${url}`;
    return `${API_BASE_URL}${formattedPath}`;
};