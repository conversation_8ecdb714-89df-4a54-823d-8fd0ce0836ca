import { ConfigProvider, theme, App as AntdApp } from 'antd';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import AuthPage from './pages/AuthPage';
import AdminLoginPage from './pages/AdminLoginPage';
import UserDashboard from './pages/UserDashboard';
import AdminDashboard from './pages/AdminDashboard';
import ProtectedRoute from './components/ProtectedRoute';
import FaceManagementPage from './pages/FaceManagementPage';
import AccountSettings from './components/AccountSettings';
import MyAccessLogs from './pages/MyAccessLogs';
import UserManagement from './components/admin/UserManagement';
import AdminAccountSettings from './components/admin/AdminAccountSettings';
import DeviceManagement from './components/admin/DeviceManagement';
import AccessLogManagement from './components/admin/AccessLogManagement';
import SystemSettings from './components/admin/SystemSettings';
import ErrorBoundary from './components/ErrorBoundary';

const App: React.FC = () => {
  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: '#FA8C16', // Orange
        },
      }}
    >
      <AntdApp>
        <Router>
        <Routes>
          <Route path="/" element={<AuthPage />} />
          <Route path="/admin/login" element={<AdminLoginPage />} />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute tokenKey="authToken" redirectTo="/">
                <UserDashboard />
              </ProtectedRoute>
            }
          >
            {/* Index route: default content for /dashboard */}
            <Route index element={<AccountSettings />} />
            <Route path="account" element={<AccountSettings />} />
            <Route path="face-management" element={<FaceManagementPage />} />
            <Route path="my-logs" element={<MyAccessLogs />} />
          </Route>
          
          <Route
            path="/admin"
            element={
              <ProtectedRoute tokenKey="adminAuthToken" redirectTo="/admin/login">
                <AdminDashboard />
              </ProtectedRoute>
            }
          >
            {/* 管理员嵌套路由 */}
            <Route path="users" element={
              <ErrorBoundary>
                <UserManagement />
              </ErrorBoundary>
            } />
            <Route path="account" element={
              <ErrorBoundary>
                <AdminAccountSettings />
              </ErrorBoundary>
            } />
            <Route path="devices" element={
              <ErrorBoundary>
                <DeviceManagement />
              </ErrorBoundary>
            } />
            <Route path="logs" element={
              <ErrorBoundary>
                <AccessLogManagement />
              </ErrorBoundary>
            } />
            <Route path="settings" element={
              <ErrorBoundary>
                <SystemSettings />
              </ErrorBoundary>
            } />
          </Route>
        </Routes>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;
