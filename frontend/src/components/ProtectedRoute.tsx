import React from 'react';
import { Navigate } from 'react-router-dom';

interface ProtectedRouteProps {
    tokenKey: string;
    redirectTo: string;
    children: React.ReactElement;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ tokenKey, redirectTo, children }) => {
    const token = localStorage.getItem(tokenKey);

    if (!token) {
        // If no token is found, redirect to the specified login page
        return <Navigate to={redirectTo} replace />;
    }

    // If a token exists, render the requested component
    return children;
};

export default ProtectedRoute;