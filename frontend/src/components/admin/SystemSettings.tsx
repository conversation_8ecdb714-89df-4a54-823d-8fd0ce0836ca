import React, { useState, useEffect } from 'react';
import {
    Card,
    Form,
    Input,
    Button,
    Space,
    Typography,
    Divider,
    message,
    Alert
} from 'antd';
import {
    SettingOutlined,
    SaveOutlined,
    ReloadOutlined,
    InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

// 系统设置数据接口 - 仅包含真实有效的参数
interface SystemSettings {
    // 基础设置
    system_name: string;
    timezone: string;
    language: string;
}

const SystemSettings: React.FC = () => {
    const [form] = Form.useForm();
    const [saveLoading, setSaveLoading] = useState(false);
    const [settings, setSettings] = useState<SystemSettings>({
        // 基础设置 - 仅包含真实有效的参数
        system_name: '智能门禁管理系统',
        timezone: 'Asia/Shanghai',
        language: 'zh-CN',
    });

    // 加载系统设置
    const loadSettings = async () => {
        try {
            // 注意：当前为演示模式，使用本地存储的默认设置
            // 在生产环境中，这里应该调用真实的API接口
            form.setFieldsValue(settings);
            message.info('已加载默认系统设置（演示模式）');
        } catch (error) {
            message.error('加载系统设置失败');
        }
    };

    // 保存系统设置
    const handleSave = async () => {
        try {
            const values = await form.validateFields();
            setSaveLoading(true);

            // 注意：当前为演示模式，仅更新本地状态
            // 在生产环境中，这里应该调用真实的API接口保存到数据库
            await new Promise(resolve => setTimeout(resolve, 500));

            setSettings(values);
            message.success('系统设置已保存（演示模式）');

        } catch (error) {
            message.error('保存设置失败');
        } finally {
            setSaveLoading(false);
        }
    };

    // 重置设置
    const handleReset = () => {
        form.setFieldsValue(settings);
        message.info('设置已重置');
    };

    useEffect(() => {
        loadSettings();
    }, []);

    return (
        <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '24px' }}>
                <Title level={2}>
                    <SettingOutlined /> 系统设置
                </Title>
                <Text type="secondary">
                    配置系统基础参数（当前为演示模式）
                </Text>
            </div>

            {/* 功能说明提示 */}
            <Alert
                message="功能说明"
                description={
                    <div>
                        <Paragraph>
                            当前系统设置页面仅包含基础的系统配置参数。更多高级功能（如门禁参数、安全策略、通知设置等）需要在后端实现相应的API接口后才能生效。
                        </Paragraph>
                        <Paragraph style={{ marginBottom: 0 }}>
                            <strong>注意：</strong>当前为演示模式，设置仅保存在本地状态中，页面刷新后会恢复默认值。
                        </Paragraph>
                    </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: '24px' }}
            />

            <Form
                form={form}
                layout="vertical"
                initialValues={settings}
                onFinish={handleSave}
            >
                {/* 基础设置 */}
                <Card
                    title={
                        <Space>
                            <SettingOutlined />
                            <span>基础设置</span>
                        </Space>
                    }
                    style={{ marginBottom: '24px' }}
                >
                    <Form.Item
                        name="system_name"
                        label="系统名称"
                        tooltip="显示在系统界面中的名称"
                        rules={[
                            { required: true, message: '请输入系统名称' },
                            { max: 50, message: '系统名称不能超过50个字符' }
                        ]}
                    >
                        <Input placeholder="请输入系统名称" />
                    </Form.Item>

                    <Form.Item
                        name="timezone"
                        label="时区设置"
                        tooltip="系统使用的时区，影响时间显示格式"
                        rules={[{ required: true, message: '请选择时区' }]}
                    >
                        <Input.Group compact>
                            <Input
                                style={{ width: '70%' }}
                                value="Asia/Shanghai"
                                disabled
                                addonBefore="当前时区"
                            />
                            <Button
                                style={{ width: '30%' }}
                                disabled
                                title="时区修改功能需要后端API支持"
                            >
                                修改时区
                            </Button>
                        </Input.Group>
                    </Form.Item>

                    <Form.Item
                        name="language"
                        label="系统语言"
                        tooltip="系统界面显示语言"
                        rules={[{ required: true, message: '请选择系统语言' }]}
                    >
                        <Input.Group compact>
                            <Input
                                style={{ width: '70%' }}
                                value="简体中文"
                                disabled
                                addonBefore="当前语言"
                            />
                            <Button
                                style={{ width: '30%' }}
                                disabled
                                title="语言切换功能需要后端API支持"
                            >
                                切换语言
                            </Button>
                        </Input.Group>
                    </Form.Item>
                </Card>

                {/* 操作按钮 */}
                <Card style={{ marginTop: '24px' }}>
                    <Space>
                        <Button
                            type="primary"
                            icon={<SaveOutlined />}
                            loading={saveLoading}
                            htmlType="submit"
                        >
                            保存设置
                        </Button>
                        <Button
                            icon={<ReloadOutlined />}
                            onClick={handleReset}
                        >
                            重置
                        </Button>
                    </Space>

                    <Divider />

                    <Alert
                        message="开发说明"
                        description={
                            <div>
                                <Paragraph>
                                    <InfoCircleOutlined style={{ marginRight: 8 }} />
                                    <strong>当前状态：</strong>演示模式，设置仅保存在前端本地状态中
                                </Paragraph>
                                <Paragraph>
                                    <strong>待开发功能：</strong>
                                </Paragraph>
                                <ul style={{ marginLeft: 16, marginBottom: 0 }}>
                                    <li>门禁参数配置（访问超时、人脸识别阈值、重试次数等）</li>
                                    <li>安全策略设置（审计日志、会话超时、双因素认证等）</li>
                                    <li>通知系统配置（邮件通知、短信通知、事件订阅等）</li>
                                    <li>系统备份和恢复功能</li>
                                </ul>
                            </div>
                        }
                        type="warning"
                        showIcon
                    />
                </Card>
            </Form>
        </div>
    );
};

export default SystemSettings;
