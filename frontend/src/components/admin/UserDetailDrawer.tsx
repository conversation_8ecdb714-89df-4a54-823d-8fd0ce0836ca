import React from 'react';
import { 
    Drawer, Descriptions, Avatar, Tag, Badge, Space, Typography, 
    Card, Row, Col, Statistic, Divider 
} from 'antd';
import {
    UserOutlined, MailOutlined, CalendarOutlined, ClockCircleOutlined,
    CrownOutlined, IdcardOutlined, EyeOutlined, SafetyCertificateOutlined,
    BarChartOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface User {
    id: number;
    username: string;
    email: string;
    is_staff: boolean;
    is_active: boolean;
    is_superuser: boolean;
    last_login: string;
    date_joined: string;
    profile: {
        avatar: string;
        nfc_card_id: string;
    };
    face_data_registered: boolean;
    access_logs_count: number;
}

interface UserDetailDrawerProps {
    visible: boolean;
    user: User | null;
    onClose: () => void;
}

const UserDetailDrawer: React.FC<UserDetailDrawerProps> = ({
    visible,
    user,
    onClose,
}) => {
    if (!user) return null;

    const formatDate = (dateString: string) => {
        if (!dateString) return '从未';
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getUserStatusColor = (user: User) => {
        if (!user.is_active) return 'red';
        if (user.is_superuser) return 'gold';
        if (user.is_staff) return 'blue';
        return 'green';
    };

    const getUserStatusText = (user: User) => {
        if (!user.is_active) return '已停用';
        if (user.is_superuser) return '超级管理员';
        if (user.is_staff) return '管理员';
        return '普通用户';
    };

    return (
        <Drawer
            title={
                <Space>
                    <UserOutlined />
                    <span>用户详情</span>
                </Space>
            }
            placement="right"
            width={600}
            open={visible}
            onClose={onClose}
        >
            {/* 用户头像和基本信息 */}
            <Card style={{ marginBottom: 16 }}>
                <div style={{ textAlign: 'center', marginBottom: 16 }}>
                    <Avatar 
                        size={80} 
                        src={user.profile?.avatar} 
                        icon={<UserOutlined />}
                        style={{ marginBottom: 8 }}
                    />
                    <div>
                        <Title level={4} style={{ margin: 0 }}>
                            {user.username}
                        </Title>
                        <Text type="secondary">@{user.username}</Text>
                    </div>
                    <div style={{ marginTop: 8 }}>
                        <Tag 
                            color={getUserStatusColor(user)}
                            icon={user.is_staff ? <CrownOutlined /> : <UserOutlined />}
                        >
                            {getUserStatusText(user)}
                        </Tag>
                    </div>
                </div>
            </Card>

            {/* 统计信息 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={12}>
                    <Card>
                        <Statistic
                            title="访问次数"
                            value={user.access_logs_count}
                            prefix={<BarChartOutlined />}
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Card>
                </Col>
                <Col span={12}>
                    <Card>
                        <Statistic
                            title="账户状态"
                            value={user.is_active ? '正常' : '停用'}
                            prefix={<SafetyCertificateOutlined />}
                            valueStyle={{ 
                                color: user.is_active ? '#52c41a' : '#ff4d4f' 
                            }}
                        />
                    </Card>
                </Col>
            </Row>

            {/* 详细信息 */}
            <Card title="基本信息" style={{ marginBottom: 16 }}>
                <Descriptions column={1} size="small">
                    <Descriptions.Item 
                        label={<Space><UserOutlined />用户名</Space>}
                    >
                        {user.username}
                    </Descriptions.Item>
                    
                    <Descriptions.Item 
                        label={<Space><MailOutlined />邮箱</Space>}
                    >
                        {user.email}
                    </Descriptions.Item>
                    

                    
                    <Descriptions.Item 
                        label={<Space><CalendarOutlined />注册时间</Space>}
                    >
                        {formatDate(user.date_joined)}
                    </Descriptions.Item>
                    
                    <Descriptions.Item 
                        label={<Space><ClockCircleOutlined />最后登录</Space>}
                    >
                        {formatDate(user.last_login)}
                    </Descriptions.Item>
                </Descriptions>
            </Card>

            {/* 功能状态 */}
            <Card title="功能状态" style={{ marginBottom: 16 }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        padding: '8px 0'
                    }}>
                        <Space>
                            <EyeOutlined />
                            <span>人脸识别</span>
                        </Space>
                        <Badge 
                            status={user.face_data_registered ? 'success' : 'default'} 
                            text={user.face_data_registered ? '已注册' : '未注册'}
                        />
                    </div>
                    
                    <Divider style={{ margin: '8px 0' }} />
                    
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        padding: '8px 0'
                    }}>
                        <Space>
                            <IdcardOutlined />
                            <span>NFC卡片</span>
                        </Space>
                        {user.profile?.nfc_card_id ? (
                            <Space>
                                <Badge status="success" text="已绑定" />
                                <Text code>{user.profile.nfc_card_id}</Text>
                            </Space>
                        ) : (
                            <Badge status="default" text="未绑定" />
                        )}
                    </div>
                </Space>
            </Card>

            {/* 权限信息 */}
            <Card title="权限信息">
                <Space direction="vertical" style={{ width: '100%' }}>
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        padding: '4px 0'
                    }}>
                        <span>账户状态</span>
                        <Tag color={user.is_active ? 'green' : 'red'}>
                            {user.is_active ? '激活' : '停用'}
                        </Tag>
                    </div>
                    
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        padding: '4px 0'
                    }}>
                        <span>管理员权限</span>
                        <Tag color={user.is_staff ? 'blue' : 'default'}>
                            {user.is_staff ? '是' : '否'}
                        </Tag>
                    </div>
                    
                    <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        padding: '4px 0'
                    }}>
                        <span>超级管理员</span>
                        <Tag color={user.is_superuser ? 'gold' : 'default'}>
                            {user.is_superuser ? '是' : '否'}
                        </Tag>
                    </div>
                </Space>
            </Card>
        </Drawer>
    );
};

export default UserDetailDrawer;
