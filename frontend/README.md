# 智能门禁管理系统 - 前端

本项目是智能门禁系统的前端部分，负责为普通用户和管理员提供交互界面。

该项目使用 [Vite](https://vitejs.dev/) 构建，技术栈为 [React](https://react.dev/) + [TypeScript](https://www.typescriptlang.org/)，并使用 [Ant Design](https://ant.design/) 作为主要的 UI 组件库。

## ✨ 功能特性

-   **用户视图**:
    -   登录与认证。
    -   个人账户信息管理。
    -   人脸数据注册与更新。
    -   查看个人门禁访问记录。
-   **管理员视图**:
    -   独立的管理员登录。
    -   **实时监控**: 通过 WebSocket 实时接收并展示设备状态和门禁日志。
    -   **用户管理**: 创建、查看、编辑和删除系统中的所有用户。
    -   **设备与监控**:
        -   通过下拉菜单选择并查看不同摄像头的**实时视频流**。
        -   监控所有已连接硬件设备的状态列表。
    -   **日志审查**: 查看和搜索完整的系统访问日志。
    -   系统设置与个人资料管理。

## 🛠️ 技术栈

-   **构建工具**: Vite
-   **核心框架**: React, TypeScript
-   **UI 组件库**: Ant Design
-   **路由**: React Router
-   **HTTP 请求**: Axios
-   **代码规范**: ESLint

## 🚀 快速开始

### 1. 环境配置

在运行前端项目之前，你需要配置后端服务的地址。

首先，将环境配置模板文件 `.env.example` 复制一份并重命名为 `.env`：

```bash
cp .env.example .env
```

然后，打开新建的 `.env` 文件，修改其中的变量。

```env
# .env

# 请将下面的IP地址替换为你的Django后端服务的实际地址。
# 如果后端运行在WSL2中，你可以在Windows终端中运行 `wsl hostname -I` 命令来获取它。
VITE_BACKEND_TARGET=http://127.0.0.1:8000
VITE_WS_TARGET=ws://127.0.0.1:8000
```

-   `VITE_BACKEND_TARGET`: 后端 Django 服务的 HTTP 地址。
-   `VITE_WS_TARGET`: 后端 Django Channels 服务的 WebSocket 地址。

`vite.config.ts` 中配置了代理，所有 `/api`, `/ws`, `/media` 的请求都会被转发到你在此处配置的地址。

### 2. 安装依赖

进入前端项目目录并执行以下命令安装所有依赖项：

```bash
cd frontend
npm install
```

### 3. 运行项目

安装完成后，运行开发服务器：

```bash
npm run dev
```

服务启动后，你可以在浏览器中访问 `http://localhost:5173` (或命令行提示的其他地址) 来查看应用。

## 📜 可用脚本

在 `package.json` 文件中，定义了以下几个可用的脚本：

-   `npm run dev`: 启动 Vite 开发服务器，支持热更新。
-   `npm run build`: 使用 TypeScript 编译并使用 Vite 打包生产环境的应用。打包后的文件会输出到 `dist` 目录。
-   `npm run lint`: 使用 ESLint 检查代码规范。
-   `npm run preview`: 在本地启动一个静态服务器，用于预览生产环境打包后的应用。
