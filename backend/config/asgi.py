import os

from channels.routing import Protocol<PERSON>ype<PERSON>out<PERSON>, URLRouter
from django.core.asgi import get_asgi_application
import access_control.routing
from access_control.middleware import TokenAuthMiddlewareStack

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

django_asgi_app = get_asgi_application()

application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": TokenAuthMiddlewareStack(
        URLRouter(
            access_control.routing.websocket_urlpatterns
        )
    ),
})
