# Production Environment Configuration
# Copy this file to .env.production and update the values for your production environment

# CRITICAL: Generate a new, strong secret key for production!
# Use: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
DJANGO_SECRET_KEY=CHANGE-THIS-TO-A-STRONG-SECRET-KEY-FOR-PRODUCTION

# Debug mode (MUST be False in production)
DJANGO_DEBUG=False

# Allowed hosts (your actual domain names)
DJANGO_ALLOWED_HOSTS=your-domain.com,www.your-domain.com,api.your-domain.com

# HTTPS Configuration (MUST be True in production)
USE_HTTPS=True

# CORS Configuration for Production
# Only allow your actual frontend domains
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com,https://app.your-domain.com

# Database Configuration for Production
DB_ENGINE=django.db.backends.postgresql
DB_NAME=access_control_prod
DB_USER=your_db_user
DB_PASSWORD=your_secure_db_password
DB_HOST=localhost
DB_PORT=5432

# WebSocket Configuration
# Redis for production WebSocket scaling
REDIS_URL=redis://localhost:6379/0

# Media and Static Files
MEDIA_ROOT=/var/www/access_control/media
STATIC_ROOT=/var/www/access_control/static

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Logging Configuration
LOG_LEVEL=WARNING
LOG_FILE=/var/log/access_control/django.log

# SSL/TLS Configuration
# If using a reverse proxy (nginx, apache), configure these
# SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https

# Additional Security Headers
# These are automatically enabled when USE_HTTPS=True
# SECURE_SSL_REDIRECT=True
# SECURE_HSTS_SECONDS=31536000
# SECURE_HSTS_INCLUDE_SUBDOMAINS=True
# SECURE_HSTS_PRELOAD=True
# SECURE_CONTENT_TYPE_NOSNIFF=True
# SECURE_BROWSER_XSS_FILTER=True
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# WebSocket Security
# Device authentication keys (in production, store these securely)
# DEVICE_AUTH_KEYS=device_001:secret_key_001,device_002:secret_key_002
