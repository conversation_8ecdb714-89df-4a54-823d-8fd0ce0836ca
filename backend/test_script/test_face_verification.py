import os
import django
import numpy as np
import sys

# --- Path Setup ---
# This allows the script to find Django modules when run from a sub-folder.
# It adds the parent directory (backend/) to the Python path.
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.append(project_root)
# --- End Path Setup ---


# --- Django Setup ---
# This allows the script to access Django models and services
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()
# --- End Django Setup ---

from django.contrib.auth.models import User
from face_db.models import FaceData
from face_db.services import verify_face

# --- Configuration ---
# The ID of the user whose face we have already registered
# We assume the user created via the previous script has ID=1.
# If not, please adjust this ID.
TARGET_USER_ID = 1

# The two images we want to test against the registered face
IMAGE_TO_TEST_1 = "test_face2.jpg"
IMAGE_TO_TEST_2 = "test_face3.jpg"


def run_verification_test():
    """
    Retrieves a user's stored embedding and verifies it against new images.
    """
    print(f"--- Starting Face Verification Test ---")
    
    # 1. Retrieve the stored embedding from the database
    try:
        print(f"Fetching registered face data for user with ID: {TARGET_USER_ID}...")
        face_data = FaceData.objects.get(user__id=TARGET_USER_ID)
        
        # The embedding is stored as bytes, so we need to convert it back to a numpy array
        stored_embedding = np.frombuffer(face_data.embedding, dtype=np.float32)
        
        print(f"Successfully retrieved embedding for user '{face_data.user.username}'.")
    except FaceData.DoesNotExist:
        print(f"Error: No face data found for user with ID {TARGET_USER_ID}. Please register a face for this user first.")
        return
    except Exception as e:
        print(f"An error occurred while fetching data: {e}")
        return

    # 2. Verify the first test image
    print(f"\nVerifying image 1: '{IMAGE_TO_TEST_1}'...")
    if os.path.exists(IMAGE_TO_TEST_1):
        is_match_1 = verify_face(IMAGE_TO_TEST_1, stored_embedding)
        print(f"--> Result for '{IMAGE_TO_TEST_1}': {'MATCH' if is_match_1 else 'NO MATCH'}")
    else:
        print(f"--> Error: Image file not found at '{IMAGE_TO_TEST_1}'")

    # 3. Verify the second test image
    print(f"\nVerifying image 2: '{IMAGE_TO_TEST_2}'...")
    if os.path.exists(IMAGE_TO_TEST_2):
        is_match_2 = verify_face(IMAGE_TO_TEST_2, stored_embedding)
        print(f"--> Result for '{IMAGE_TO_TEST_2}': {'MATCH' if is_match_2 else 'NO MATCH'}")
    else:
        print(f"--> Error: Image file not found at '{IMAGE_TO_TEST_2}'")
        
    print("\n--- Test Complete ---")


if __name__ == "__main__":
    run_verification_test()
