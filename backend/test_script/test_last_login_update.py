#!/usr/bin/env python3
"""
测试用户登录时last_login字段更新功能
验证用户登录和管理员登录是否正确记录最后登录时间
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User

def test_last_login_update():
    """测试登录时last_login字段更新功能"""
    base_url = 'http://127.0.0.1:8000'
    
    print("🔧 开始测试登录时间更新功能...")
    print("=" * 60)
    
    # 1. 获取测试用户
    print("1️⃣ 准备测试用户...")
    
    try:
        # 查找test用户
        test_user = User.objects.get(username='test')
        print(f"   找到测试用户: {test_user.username}")
        print(f"   登录前last_login: {test_user.last_login}")
        
        # 查找管理员用户
        admin_user = User.objects.filter(is_superuser=True).first()
        if admin_user:
            print(f"   找到管理员用户: {admin_user.username}")
            print(f"   登录前last_login: {admin_user.last_login}")
        else:
            print("   ❌ 没有找到管理员用户")
            return False
            
    except User.DoesNotExist:
        print("   ❌ 没有找到test用户")
        return False
    
    # 2. 测试普通用户登录
    print("\n2️⃣ 测试普通用户登录...")
    
    login_data = {
        'username': 'test',
        'password': 'testtest'
    }
    
    try:
        response = requests.post(f'{base_url}/api/auth/login/', json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            print(f"   ✅ 普通用户登录成功，Token: {token_data['token'][:20]}...")
            
            # 检查数据库中的last_login是否更新
            test_user.refresh_from_db()
            print(f"   登录后last_login: {test_user.last_login}")
            
            if test_user.last_login:
                print("   ✅ last_login字段已更新")
            else:
                print("   ❌ last_login字段未更新")
                return False
        else:
            print(f"   ❌ 普通用户登录失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 普通用户登录请求失败: {e}")
        return False
    
    # 3. 测试管理员登录
    print("\n3️⃣ 测试管理员登录...")
    
    admin_login_data = {
        'username': admin_user.username,
        'password': 'admin123'  # 假设管理员密码
    }
    
    try:
        response = requests.post(f'{base_url}/api/auth/login/', json=admin_login_data)
        if response.status_code == 200:
            token_data = response.json()
            print(f"   ✅ 管理员登录成功，Token: {token_data['token'][:20]}...")
            
            # 检查数据库中的last_login是否更新
            admin_user.refresh_from_db()
            print(f"   登录后last_login: {admin_user.last_login}")
            
            if admin_user.last_login:
                print("   ✅ 管理员last_login字段已更新")
            else:
                print("   ❌ 管理员last_login字段未更新")
                return False
        else:
            print(f"   ❌ 管理员登录失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 管理员登录请求失败: {e}")
        return False
    
    # 4. 验证管理员API中的last_login显示
    print("\n4️⃣ 验证管理员API中的last_login显示...")
    
    admin_token = token_data['token']
    headers = {'Authorization': f'Token {admin_token}'}
    
    try:
        response = requests.get(f'{base_url}/api/admin/users/', headers=headers)
        if response.status_code == 200:
            users_data = response.json()
            users = users_data.get('results', [])
            
            print(f"   ✅ 获取用户列表成功，共 {len(users)} 个用户")
            
            # 检查test用户的last_login
            test_user_data = next((u for u in users if u['username'] == 'test'), None)
            if test_user_data:
                last_login = test_user_data.get('last_login')
                print(f"   test用户API返回的last_login: {last_login}")
                if last_login:
                    print("   ✅ API正确返回了last_login信息")
                else:
                    print("   ❌ API未返回last_login信息")
                    return False
            else:
                print("   ❌ 在API响应中未找到test用户")
                return False
                
        else:
            print(f"   ❌ 获取用户列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 管理员API请求失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有登录时间更新测试通过！")
    return True

if __name__ == '__main__':
    success = test_last_login_update()
    if success:
        print("\n✅ 测试结果: 登录时间更新功能正常工作")
    else:
        print("\n❌ 测试结果: 登录时间更新功能存在问题")
        sys.exit(1)
