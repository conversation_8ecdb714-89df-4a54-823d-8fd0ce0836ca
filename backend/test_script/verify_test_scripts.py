#!/usr/bin/env python3
"""
验证测试脚本路径配置

检查移动到test_script目录后的脚本是否能正常工作
"""

import os
import sys
from pathlib import Path

def check_django_setup():
    """检查Django环境设置"""
    print("🔍 检查Django环境设置...")
    
    try:
        # 设置Django环境（与测试脚本相同的逻辑）
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
        
        print(f"   当前脚本位置: {Path(__file__)}")
        print(f"   项目根目录: {project_root}")
        print(f"   项目根目录存在: {project_root.exists()}")
        print(f"   manage.py存在: {(project_root / 'manage.py').exists()}")
        print(f"   config目录存在: {(project_root / 'config').exists()}")
        
        # 尝试导入Django
        import django
        django.setup()
        print("   ✅ Django环境设置成功")
        
        # 测试模型导入
        from django.contrib.auth.models import User
        from face_db.models import FaceData
        from rest_framework.authtoken.models import Token
        print("   ✅ 模型导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Django环境设置失败: {e}")
        return False

def check_test_images():
    """检查测试图片文件"""
    print("\n🖼️  检查测试图片文件...")
    
    current_dir = Path(__file__).parent
    test_images = [
        "test_face.jpg",
        "test_face2.jpg", 
        "test_face3.jpg"
    ]
    
    for image_name in test_images:
        image_path = current_dir / image_name
        if image_path.exists():
            file_size = image_path.stat().st_size
            print(f"   ✅ {image_name}: {file_size} bytes ({file_size/1024:.1f} KB)")
        else:
            print(f"   ❌ {image_name}: 不存在")
    
    return True

def check_script_syntax():
    """检查脚本语法"""
    print("\n📝 检查脚本语法...")
    
    current_dir = Path(__file__).parent
    test_scripts = [
        "test_real_face.py",
        "test_face_upload.py",
        "test_face_data_integrity.py"
    ]
    
    for script_name in test_scripts:
        script_path = current_dir / script_name
        if script_path.exists():
            try:
                # 编译检查语法
                with open(script_path, 'r', encoding='utf-8') as f:
                    source = f.read()
                compile(source, str(script_path), 'exec')
                print(f"   ✅ {script_name}: 语法正确")
            except SyntaxError as e:
                print(f"   ❌ {script_name}: 语法错误 - {e}")
            except Exception as e:
                print(f"   ⚠️  {script_name}: 检查异常 - {e}")
        else:
            print(f"   ❌ {script_name}: 文件不存在")
    
    return True

def test_import_paths():
    """测试导入路径"""
    print("\n🔗 测试导入路径...")
    
    try:
        # 测试Django相关导入
        from django.contrib.auth.models import User
        print("   ✅ Django User模型导入成功")
        
        from face_db.models import FaceData
        print("   ✅ FaceData模型导入成功")
        
        from rest_framework.authtoken.models import Token
        print("   ✅ Token模型导入成功")
        
        # 测试其他依赖
        import requests
        print("   ✅ requests库导入成功")
        
        from PIL import Image
        print("   ✅ PIL库导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 验证测试脚本配置")
    print("=" * 50)
    
    results = []
    
    # 1. 检查Django环境
    results.append(check_django_setup())
    
    # 2. 检查测试图片
    results.append(check_test_images())
    
    # 3. 检查脚本语法
    results.append(check_script_syntax())
    
    # 4. 测试导入路径
    results.append(test_import_paths())
    
    print("\n" + "=" * 50)
    print("📊 验证结果:")
    
    test_names = [
        "Django环境设置",
        "测试图片文件",
        "脚本语法检查", 
        "导入路径测试"
    ]
    
    all_passed = True
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有验证通过！测试脚本可以正常使用")
        print("\n📋 使用方法:")
        print("   cd backend")
        print("   source venv/bin/activate")
        print("   python test_script/test_real_face.py")
        print("   python test_script/test_face_upload.py")
        print("   python test_script/test_face_data_integrity.py")
    else:
        print("\n⚠️  部分验证失败，请检查配置")

if __name__ == "__main__":
    main()
