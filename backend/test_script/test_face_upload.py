#!/usr/bin/env python3
"""
测试人脸上传功能

创建一个有效的测试图片并测试上传功能
"""

import os
import sys
import django
from pathlib import Path
import base64
import requests
from PIL import Image, ImageDraw
import io

# 设置Django环境
# 从test_script目录回到backend目录
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

def create_test_image():
    """创建一个简单的测试图片"""
    print("🎨 创建测试图片...")
    
    # 创建一个简单的图片（模拟人脸）
    img = Image.new('RGB', (200, 200), color='white')
    draw = ImageDraw.Draw(img)
    
    # 画一个简单的"脸"
    # 脸部轮廓
    draw.ellipse([50, 50, 150, 150], fill='lightpink', outline='black')
    # 眼睛
    draw.ellipse([70, 80, 80, 90], fill='black')
    draw.ellipse([120, 80, 130, 90], fill='black')
    # 鼻子
    draw.ellipse([95, 100, 105, 110], fill='pink')
    # 嘴巴
    draw.arc([80, 115, 120, 135], 0, 180, fill='red', width=2)
    
    # 保存为字节流
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='JPEG')
    img_byte_arr = img_byte_arr.getvalue()
    
    # 转换为base64
    base64_image = "data:image/jpeg;base64," + base64.b64encode(img_byte_arr).decode()
    
    print(f"   ✅ 测试图片创建成功，大小: {len(img_byte_arr)} bytes")
    return base64_image

def get_user_token(username='test1'):
    """获取用户Token"""
    print(f"🔑 获取用户 {username} 的Token...")
    
    try:
        user = User.objects.get(username=username)
        token, created = Token.objects.get_or_create(user=user)
        print(f"   ✅ Token: {token.key[:10]}...")
        return token.key
    except User.DoesNotExist:
        print(f"   ❌ 用户 {username} 不存在")
        return None

def test_face_upload():
    """测试人脸上传"""
    print("\n🧪 测试人脸上传...")
    
    # 获取Token
    token = get_user_token()
    if not token:
        return False
    
    # 创建测试图片
    test_image = create_test_image()
    
    # 准备API请求
    url = "http://127.0.0.1:8000/api/faces/register/"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Token {token}',
    }
    data = {
        'image': test_image
    }
    
    print(f"   发送请求到: {url}")
    print(f"   图片数据长度: {len(test_image)} 字符")
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("   ✅ 上传成功!")
            try:
                response_data = response.json()
                print(f"   响应数据: {response_data}")
                return True
            except:
                print(f"   响应内容: {response.text}")
                return True
        else:
            print(f"   ❌ 上传失败")
            print(f"   错误内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败 - Django服务器可能没有运行")
        return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def test_with_real_image():
    """使用真实图片测试（如果有的话）"""
    print("\n🖼️  测试真实图片上传...")

    # 检查指定的测试图片（现在脚本在test_script目录内）
    test_image_path = Path("test_face2.jpg")
    if not test_image_path.exists():
        print(f"   ℹ️  没有找到 {test_image_path}，跳过真实图片测试")
        print("   提示: 请确认测试图片路径是否正确")
        return True
    
    try:
        # 读取图片并转换为base64
        with open(test_image_path, 'rb') as f:
            image_data = f.read()
        
        base64_image = "data:image/jpeg;base64," + base64.b64encode(image_data).decode()
        
        # 获取Token
        token = get_user_token()
        if not token:
            return False
        
        # 发送请求
        url = "http://127.0.0.1:8000/api/faces/register/"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Token {token}',
        }
        data = {
            'image': base64_image
        }
        
        print(f"   发送真实图片，大小: {len(image_data)} bytes")
        
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("   ✅ 真实图片上传成功!")
            return True
        else:
            print(f"   ❌ 真实图片上传失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 真实图片测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试人脸上传功能")
    print("=" * 50)
    
    # 测试简单图片上传
    test1_success = test_face_upload()
    
    # 测试真实图片上传
    test2_success = test_with_real_image()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"   简单图片测试: {'✅ 成功' if test1_success else '❌ 失败'}")
    print(f"   真实图片测试: {'✅ 成功' if test2_success else '❌ 失败'}")
    
    if test1_success:
        print("\n🎉 修复成功！人脸上传功能正常工作")
        print("\n📋 下一步:")
        print("   1. 重启Django服务器")
        print("   2. 在前端测试人脸上传功能")
        print("   3. 检查浏览器开发者工具确认请求正常")
    else:
        print("\n⚠️  仍有问题，请检查:")
        print("   1. Django服务器是否运行")
        print("   2. DeepFace是否正确安装")
        print("   3. 后端日志输出")

if __name__ == "__main__":
    main()
