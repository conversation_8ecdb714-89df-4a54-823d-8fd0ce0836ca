# 🧪 测试脚本目录

本目录包含智能门禁系统的各种测试脚本，用于验证系统功能和配置。

## 📋 测试脚本列表

### 🔐 WebSocket 认证测试

#### `test_websocket_simple.py`
**用途**: 简单的WebSocket认证配置测试  
**功能**: 
- 测试Token认证逻辑
- 测试设备认证逻辑  
- 测试配置完整性
- 验证模块导入

**使用方法**:
```bash
cd backend
source venv/bin/activate
python test_script/test_websocket_simple.py
```

#### `test_websocket.py`
**用途**: 完整的WebSocket认证功能测试  
**功能**:
- 用户WebSocket连接测试
- 设备WebSocket连接测试
- 管理员WebSocket连接测试
- 无认证连接拒绝测试

**使用方法**:
```bash
cd backend
source venv/bin/activate
# 需要先启动Django服务器
python manage.py runserver &
python test_script/test_websocket.py [test_type]
```

**测试类型**:
- `user`: 测试用户认证
- `device`: 测试设备认证  
- `admin`: 测试管理员认证
- `all`: 运行所有测试 (默认)

### 👤 人脸识别测试

#### `test_face_registration.py`
**用途**: 人脸注册功能测试  
**功能**:
- 测试人脸特征提取
- 测试人脸数据存储
- 验证注册流程

**使用方法**:
```bash
cd backend
source venv/bin/activate
python test_script/test_face_registration.py
```

#### `test_face_verification.py`
**用途**: 人脸验证功能测试  
**功能**:
- 测试人脸识别匹配
- 测试验证准确性
- 性能基准测试

**使用方法**:
```bash
cd backend
source venv/bin/activate
python test_script/test_face_verification.py
```

### 🖼️ 测试图片

#### `test_face.jpg`, `test_face2.jpg`, `test_face3.jpg`
**用途**: 人脸识别测试用的样本图片  
**说明**: 用于测试人脸注册和验证功能

## 🚀 快速测试指南

### 1. 基础配置测试
```bash
# 检查安全配置
python check_security.py

# 检查WebSocket配置
python test_script/test_websocket_simple.py
```

### 2. 人脸识别测试
```bash
# 测试人脸注册
python test_script/test_face_registration.py

# 测试人脸验证
python test_script/test_face_verification.py
```

### 3. WebSocket功能测试
```bash
# 启动服务器
python manage.py runserver &

# 运行WebSocket测试
python test_script/test_websocket.py all
```

### 4. 完整系统测试
```bash
# 运行部署检查
./deploy_check.sh development

# 启动前后端服务
python manage.py runserver &
cd ../frontend && npm run dev &

# 在浏览器中测试: http://localhost:5173
```

## 🔧 测试环境要求

### Python依赖
```bash
pip install -r requirements.txt
pip install websockets  # WebSocket测试需要
```

### 环境配置
- 确保 `.env` 文件存在并正确配置
- 数据库迁移已完成: `python manage.py migrate`
- 虚拟环境已激活: `source venv/bin/activate`

## 📊 测试结果说明

### 成功标识
- ✅ 测试通过
- 🎉 所有测试通过

### 警告标识  
- ⚠️ 警告 (通常是开发环境的正常配置)
- 📝 注意事项

### 错误标识
- ❌ 测试失败
- 🚨 严重错误

## 🐛 故障排除

### 常见问题

1. **模块导入错误**
   ```bash
   # 确保在正确目录
   cd backend
   # 确保虚拟环境激活
   source venv/bin/activate
   ```

2. **数据库错误**
   ```bash
   # 运行迁移
   python manage.py migrate
   # 创建超级用户
   python manage.py createsuperuser
   ```

3. **WebSocket连接失败**
   ```bash
   # 检查服务器是否运行
   python manage.py runserver
   # 检查端口占用
   netstat -tlnp | grep :8000
   ```

4. **人脸识别错误**
   ```bash
   # 检查DeepFace安装
   pip install deepface
   # 检查测试图片
   ls test_script/test_face*.jpg
   ```

## 📚 相关文档

- **安全配置**: `../check_security.py`
- **部署检查**: `../deploy_check.sh`  
- **HTTPS配置**: `../docs/backend/HTTPS_SETUP.md`
- **安全指南**: `../docs/SECURITY_SETUP.md`

## 🔄 持续集成

这些测试脚本可以集成到CI/CD流程中：

```yaml
# .github/workflows/test.yml 示例
- name: Run Security Tests
  run: python check_security.py

- name: Run WebSocket Tests  
  run: python test_script/test_websocket_simple.py

- name: Run Face Recognition Tests
  run: |
    python test_script/test_face_registration.py
    python test_script/test_face_verification.py
```

---

**注意**: 在生产环境部署前，请确保所有测试都通过，特别是安全相关的测试。
