#!/usr/bin/env python
"""
WebSocket Authentication Test Script

This script tests the WebSocket authentication functionality
for both user and device connections.

Usage:
    python test_websocket.py [test_type]
    
Test types:
    - user: Test user authentication
    - device: Test device authentication
    - admin: Test admin authentication
    - all: Run all tests (default)
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).resolve().parent.parent  # Go up one level from test_script
sys.path.append(str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

try:
    import django
    django.setup()
    
    import websockets
    from django.contrib.auth.models import User
    from rest_framework.authtoken.models import Token
    from django.test import TransactionTestCase
    from channels.testing import WebsocketCommunicator
    from access_control.consumers import <PERSON><PERSON><PERSON>onsumer, AdminMonitorConsumer, UserDashboardConsumer
    
except ImportError as e:
    print(f"❌ Error importing required modules: {e}")
    print("Make sure you have installed all dependencies:")
    print("pip install websockets channels")
    sys.exit(1)


class WebSocketAuthTest:
    """WebSocket authentication test suite"""
    
    def __init__(self):
        self.base_url = "ws://127.0.0.1:8000"
        self.test_results = []
    
    async def test_user_authentication(self):
        """Test user WebSocket authentication"""
        print("\n🧪 Testing User Authentication...")
        
        try:
            # Create test user and token
            user, created = User.objects.get_or_create(
                username='test_ws_user',
                defaults={'email': '<EMAIL>'}
            )
            token, created = Token.objects.get_or_create(user=user)
            
            # Test with valid token
            communicator = WebsocketCommunicator(
                UserDashboardConsumer.as_asgi(),
                f"/ws/user/dashboard/?token={token.key}"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if connected:
                print("✅ User authentication successful")
                
                # Test sending a command
                await communicator.send_json_to({
                    'command': 'get_my_profile'
                })
                
                response = await communicator.receive_json_from()
                print(f"📨 Received: {response.get('type', 'unknown')}")
                
                await communicator.disconnect()
                self.test_results.append(("User Auth", True, "Valid token accepted"))
            else:
                print("❌ User authentication failed")
                self.test_results.append(("User Auth", False, "Valid token rejected"))
            
            # Test with invalid token
            communicator = WebsocketCommunicator(
                UserDashboardConsumer.as_asgi(),
                "/ws/user/dashboard/?token=invalid_token"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if not connected:
                print("✅ Invalid token correctly rejected")
                self.test_results.append(("User Auth Invalid", True, "Invalid token rejected"))
            else:
                print("❌ Invalid token incorrectly accepted")
                await communicator.disconnect()
                self.test_results.append(("User Auth Invalid", False, "Invalid token accepted"))
                
        except Exception as e:
            print(f"❌ User authentication test failed: {e}")
            self.test_results.append(("User Auth", False, str(e)))
    
    async def test_device_authentication(self):
        """Test device WebSocket authentication"""
        print("\n🤖 Testing Device Authentication...")
        
        try:
            device_id = "test_device_001"
            valid_key = f"device_{device_id}_secret_key"
            
            # Test with valid device key
            communicator = WebsocketCommunicator(
                DeviceConsumer.as_asgi(),
                f"/ws/device/{device_id}/?device_key={valid_key}"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if connected:
                print("✅ Device authentication successful")
                
                # Test sending NFC scan event
                await communicator.send_json_to({
                    'event': 'nfc_scan',
                    'data': {
                        'card_id': 'test_card_123',
                        'timestamp': '2025-07-10T10:00:00Z'
                    }
                })
                
                # Should receive response or error
                try:
                    response = await asyncio.wait_for(
                        communicator.receive_json_from(), 
                        timeout=2.0
                    )
                    print(f"📨 Device response: {response.get('type', 'unknown')}")
                except asyncio.TimeoutError:
                    print("⏰ No immediate response (normal for NFC scan)")
                
                await communicator.disconnect()
                self.test_results.append(("Device Auth", True, "Valid device key accepted"))
            else:
                print("❌ Device authentication failed")
                self.test_results.append(("Device Auth", False, "Valid device key rejected"))
            
            # Test with invalid device key
            communicator = WebsocketCommunicator(
                DeviceConsumer.as_asgi(),
                f"/ws/device/{device_id}/?device_key=invalid_key"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if not connected:
                print("✅ Invalid device key correctly rejected")
                self.test_results.append(("Device Auth Invalid", True, "Invalid device key rejected"))
            else:
                print("❌ Invalid device key incorrectly accepted")
                await communicator.disconnect()
                self.test_results.append(("Device Auth Invalid", False, "Invalid device key accepted"))
                
        except Exception as e:
            print(f"❌ Device authentication test failed: {e}")
            self.test_results.append(("Device Auth", False, str(e)))
    
    async def test_admin_authentication(self):
        """Test admin WebSocket authentication"""
        print("\n👑 Testing Admin Authentication...")
        
        try:
            # Create admin user and token
            admin_user, created = User.objects.get_or_create(
                username='test_admin_user',
                defaults={
                    'email': '<EMAIL>',
                    'is_staff': True,
                    'is_superuser': True
                }
            )
            admin_user.is_staff = True
            admin_user.is_superuser = True
            admin_user.save()
            
            token, created = Token.objects.get_or_create(user=admin_user)
            
            # Test admin connection
            communicator = WebsocketCommunicator(
                AdminMonitorConsumer.as_asgi(),
                f"/ws/admin/monitor/?token={token.key}"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if connected:
                print("✅ Admin authentication successful")
                
                # Test admin command
                await communicator.send_json_to({
                    'command': 'get_device_status'
                })
                
                response = await communicator.receive_json_from()
                print(f"📨 Admin response: {response.get('type', 'unknown')}")
                
                await communicator.disconnect()
                self.test_results.append(("Admin Auth", True, "Admin token accepted"))
            else:
                print("❌ Admin authentication failed")
                self.test_results.append(("Admin Auth", False, "Admin token rejected"))
            
            # Test non-admin user
            regular_user, created = User.objects.get_or_create(
                username='test_regular_user',
                defaults={'email': '<EMAIL>'}
            )
            regular_token, created = Token.objects.get_or_create(user=regular_user)
            
            communicator = WebsocketCommunicator(
                AdminMonitorConsumer.as_asgi(),
                f"/ws/admin/monitor/?token={regular_token.key}"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if not connected:
                print("✅ Non-admin user correctly rejected from admin interface")
                self.test_results.append(("Admin Auth Non-Admin", True, "Non-admin rejected"))
            else:
                print("❌ Non-admin user incorrectly allowed in admin interface")
                await communicator.disconnect()
                self.test_results.append(("Admin Auth Non-Admin", False, "Non-admin accepted"))
                
        except Exception as e:
            print(f"❌ Admin authentication test failed: {e}")
            self.test_results.append(("Admin Auth", False, str(e)))
    
    async def test_no_authentication(self):
        """Test connections without authentication"""
        print("\n🚫 Testing No Authentication...")
        
        try:
            # Test user dashboard without token
            communicator = WebsocketCommunicator(
                UserDashboardConsumer.as_asgi(),
                "/ws/user/dashboard/"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if not connected:
                print("✅ User dashboard correctly rejects unauthenticated connections")
                self.test_results.append(("No Auth User", True, "Unauthenticated rejected"))
            else:
                print("❌ User dashboard incorrectly accepts unauthenticated connections")
                await communicator.disconnect()
                self.test_results.append(("No Auth User", False, "Unauthenticated accepted"))
            
            # Test device without key
            communicator = WebsocketCommunicator(
                DeviceConsumer.as_asgi(),
                "/ws/device/test_device/"
            )
            
            connected, subprotocol = await communicator.connect()
            
            if not connected:
                print("✅ Device correctly rejects connections without device key")
                self.test_results.append(("No Auth Device", True, "No device key rejected"))
            else:
                print("❌ Device incorrectly accepts connections without device key")
                await communicator.disconnect()
                self.test_results.append(("No Auth Device", False, "No device key accepted"))
                
        except Exception as e:
            print(f"❌ No authentication test failed: {e}")
            self.test_results.append(("No Auth", False, str(e)))
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 WebSocket Authentication Test Summary")
        print("=" * 60)
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        for test_name, success, message in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}: {message}")
        
        print(f"\n📈 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All WebSocket authentication tests passed!")
        else:
            print("⚠️  Some tests failed. Please check the WebSocket authentication configuration.")
    
    async def run_all_tests(self):
        """Run all WebSocket authentication tests"""
        print("🚀 Starting WebSocket Authentication Tests...")
        
        await self.test_user_authentication()
        await self.test_device_authentication()
        await self.test_admin_authentication()
        await self.test_no_authentication()
        
        self.print_summary()


async def main():
    """Main test function"""
    test_type = sys.argv[1] if len(sys.argv) > 1 else 'all'
    
    tester = WebSocketAuthTest()
    
    if test_type == 'user':
        await tester.test_user_authentication()
    elif test_type == 'device':
        await tester.test_device_authentication()
    elif test_type == 'admin':
        await tester.test_admin_authentication()
    elif test_type == 'all':
        await tester.run_all_tests()
    else:
        print(f"Unknown test type: {test_type}")
        print("Available types: user, device, admin, all")
        return
    
    if test_type != 'all':
        tester.print_summary()


if __name__ == '__main__':
    asyncio.run(main())
