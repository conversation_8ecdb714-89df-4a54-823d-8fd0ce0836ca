#!/usr/bin/env python3
"""
人脸数据完整性测试脚本

此脚本验证数据库修复后的人脸数据完整性：
1. 检查每个用户最多只有一条人脸数据记录
2. 验证OneToOneField约束正常工作
3. 测试API的创建和更新逻辑
4. 验证前端兼容性

使用方法:
cd backend
python test_script/test_face_data_integrity.py
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from face_db.models import FaceData
from django.db import IntegrityError
import numpy as np

def test_database_constraints():
    """测试数据库约束"""
    print("🔍 测试1: 数据库约束验证")
    
    # 检查每个用户最多只有一条人脸数据
    users_with_multiple_faces = []
    total_users = User.objects.count()
    users_with_faces = 0
    
    for user in User.objects.all():
        try:
            face_data = user.face_data  # 使用OneToOneField的related_name
            users_with_faces += 1
        except FaceData.DoesNotExist:
            pass  # 用户没有人脸数据，这是正常的
        except Exception as e:
            users_with_multiple_faces.append((user.username, str(e)))
    
    print(f"   总用户数: {total_users}")
    print(f"   有人脸数据的用户: {users_with_faces}")
    print(f"   有多条人脸数据的用户: {len(users_with_multiple_faces)}")
    
    if users_with_multiple_faces:
        print("   ❌ 发现有多条人脸数据的用户:")
        for username, error in users_with_multiple_faces:
            print(f"      - {username}: {error}")
        return False
    else:
        print("   ✅ 所有用户最多只有一条人脸数据")
        return True

def test_onetoone_constraint():
    """测试OneToOneField约束"""
    print("\n🔍 测试2: OneToOneField约束验证")
    
    # 创建测试用户
    test_user, created = User.objects.get_or_create(
        username='test_face_integrity',
        defaults={'email': '<EMAIL>'}
    )
    
    if created:
        print(f"   创建测试用户: {test_user.username}")
    else:
        print(f"   使用现有测试用户: {test_user.username}")
        # 清理现有的人脸数据
        try:
            test_user.face_data.delete()
            print("   清理了现有的人脸数据")
        except FaceData.DoesNotExist:
            pass
    
    # 创建第一条人脸数据
    fake_embedding = np.random.rand(2622).astype(np.float32).tobytes()
    
    face_data1 = FaceData.objects.create(
        user=test_user,
        embedding=fake_embedding,
        image_path='test_image_1.jpg'
    )
    print(f"   ✅ 成功创建第一条人脸数据 (ID: {face_data1.id})")
    
    # 尝试创建第二条人脸数据（应该失败）
    try:
        face_data2 = FaceData.objects.create(
            user=test_user,
            embedding=fake_embedding,
            image_path='test_image_2.jpg'
        )
        print("   ❌ 错误：成功创建了第二条人脸数据，OneToOneField约束未生效")
        face_data2.delete()
        face_data1.delete()
        test_user.delete()
        return False
    except IntegrityError as e:
        print(f"   ✅ OneToOneField约束正常工作: {str(e)}")
    
    # 清理测试数据
    face_data1.delete()
    test_user.delete()
    print("   清理测试数据完成")
    return True

def test_api_logic():
    """测试API逻辑"""
    print("\n🔍 测试3: API逻辑验证")
    
    from face_db.views import FaceRegistrationAPI
    from rest_framework.test import APIRequestFactory
    from rest_framework.authtoken.models import Token
    from django.contrib.auth import authenticate
    import base64
    
    # 创建测试用户
    test_user, created = User.objects.get_or_create(
        username='test_api_user',
        defaults={'email': '<EMAIL>'}
    )
    
    if created:
        test_user.set_password('testpass123')
        test_user.save()
        print(f"   创建测试用户: {test_user.username}")
    else:
        print(f"   使用现有测试用户: {test_user.username}")
        # 清理现有的人脸数据
        try:
            test_user.face_data.delete()
            print("   清理了现有的人脸数据")
        except FaceData.DoesNotExist:
            pass
    
    # 创建或获取token
    token, created = Token.objects.get_or_create(user=test_user)
    
    # 模拟API请求
    factory = APIRequestFactory()
    
    # 创建假的base64图片数据
    fake_image_data = "data:image/jpeg;base64," + base64.b64encode(b"fake_image_data").decode()
    
    print("   测试API创建逻辑...")
    # 这里我们只测试数据模型层面，不测试DeepFace处理
    # 因为测试环境可能没有完整的DeepFace依赖
    
    # 直接测试数据库操作
    fake_embedding = np.random.rand(2622).astype(np.float32).tobytes()
    
    # 第一次创建
    face_data = FaceData.objects.create(
        user=test_user,
        embedding=fake_embedding,
        image_path='api_test_image.jpg'
    )
    print(f"   ✅ 成功创建人脸数据 (ID: {face_data.id})")
    
    # 模拟更新操作
    original_id = face_data.id
    face_data.embedding = np.random.rand(2622).astype(np.float32).tobytes()
    face_data.save()
    
    # 验证是否是更新而不是创建新记录
    updated_face_data = FaceData.objects.get(user=test_user)
    if updated_face_data.id == original_id:
        print("   ✅ 更新逻辑正常工作，没有创建新记录")
    else:
        print("   ❌ 更新逻辑有问题，创建了新记录")
        face_data.delete()
        test_user.delete()
        return False
    
    # 清理测试数据
    face_data.delete()
    test_user.delete()
    print("   清理测试数据完成")
    return True

def test_data_consistency():
    """测试数据一致性"""
    print("\n🔍 测试4: 数据一致性验证")
    
    # 检查所有人脸数据记录的完整性
    total_face_records = FaceData.objects.count()
    valid_records = 0
    invalid_records = []
    
    for face_data in FaceData.objects.all():
        try:
            # 检查用户是否存在
            user = face_data.user
            if user:
                # 检查embedding是否有效
                if face_data.embedding and len(face_data.embedding) > 0:
                    # 检查是否可以转换为numpy数组
                    embedding_array = np.frombuffer(face_data.embedding, dtype=np.float32)
                    if len(embedding_array) > 0:
                        valid_records += 1
                    else:
                        invalid_records.append(f"ID {face_data.id}: 空的embedding数组")
                else:
                    invalid_records.append(f"ID {face_data.id}: 无效的embedding数据")
            else:
                invalid_records.append(f"ID {face_data.id}: 关联的用户不存在")
        except Exception as e:
            invalid_records.append(f"ID {face_data.id}: {str(e)}")
    
    print(f"   总人脸数据记录: {total_face_records}")
    print(f"   有效记录: {valid_records}")
    print(f"   无效记录: {len(invalid_records)}")
    
    if invalid_records:
        print("   ❌ 发现无效记录:")
        for record in invalid_records:
            print(f"      - {record}")
        return False
    else:
        print("   ✅ 所有人脸数据记录都有效")
        return True

def main():
    """主测试函数"""
    print("🚀 开始人脸数据完整性测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(test_database_constraints())
    test_results.append(test_onetoone_constraint())
    test_results.append(test_api_logic())
    test_results.append(test_data_consistency())
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    test_names = [
        "数据库约束验证",
        "OneToOneField约束验证", 
        "API逻辑验证",
        "数据一致性验证"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！数据库修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查和修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
