#!/usr/bin/env python3
"""
调试前端人脸上传问题

帮助诊断前端无响应的问题
"""

import os
import sys
import django
from pathlib import Path
import base64
import requests
import json

# 设置Django环境
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token
from face_db.models import FaceData

def check_user_status():
    """检查test1用户状态"""
    print("👤 检查test1用户状态...")
    
    try:
        user = User.objects.get(username='test1')
        print(f"   ✅ 用户存在: {user.username} (ID: {user.id})")
        print(f"   用户状态: {'活跃' if user.is_active else '非活跃'}")
        
        # 检查Token
        try:
            token = Token.objects.get(user=user)
            print(f"   ✅ Token存在: {token.key[:10]}...")
        except Token.DoesNotExist:
            print("   ❌ Token不存在")
            return None
        
        # 检查人脸数据
        try:
            face_data = user.face_data
            print(f"   ✅ 人脸数据存在:")
            print(f"     - ID: {face_data.id}")
            print(f"     - 创建: {face_data.created_at}")
            print(f"     - 更新: {face_data.updated_at}")
        except FaceData.DoesNotExist:
            print("   ℹ️  无人脸数据")
        
        return token.key
        
    except User.DoesNotExist:
        print("   ❌ 用户test1不存在")
        return None

def test_api_endpoint():
    """测试API端点"""
    print("\n🔗 测试API端点...")
    
    token = check_user_status()
    if not token:
        return False
    
    # 测试获取人脸数据API
    print("\n   测试获取人脸数据API...")
    try:
        response = requests.get(
            "http://127.0.0.1:8000/api/faces/",
            headers={'Authorization': f'Token {token}'},
            timeout=10
        )
        print(f"   GET /api/faces/ - 状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据: {data}")
        else:
            print(f"   错误响应: {response.text}")
    except Exception as e:
        print(f"   ❌ GET请求失败: {e}")
    
    # 测试上传API
    print("\n   测试上传API...")
    try:
        # 使用指定的测试图片（获取脚本所在目录的图片）
        script_dir = Path(__file__).parent
        test_image_path = script_dir / "test_face.jpg"
        print(f"   查找测试图片: {test_image_path}")
        print(f"   图片存在: {test_image_path.exists()}")
        if test_image_path.exists():
            with open(test_image_path, 'rb') as f:
                image_data = f.read()
            base64_image = "data:image/jpeg;base64," + base64.b64encode(image_data).decode()
            
            response = requests.post(
                "http://127.0.0.1:8000/api/faces/register/",
                json={'image': base64_image},
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Token {token}'
                },
                timeout=30
            )
            print(f"   POST /api/faces/register/ - 状态码: {response.status_code}")
            if response.status_code in [200, 201]:
                print(f"   ✅ 上传成功: {response.json()}")
                return True
            else:
                print(f"   ❌ 上传失败: {response.text}")
                return False
        else:
            print("   ⚠️  测试图片不存在，跳过上传测试")
            return True
            
    except Exception as e:
        print(f"   ❌ POST请求失败: {e}")
        return False

def check_cors_and_headers():
    """检查CORS和请求头"""
    print("\n🌐 检查CORS和请求头...")
    
    try:
        # 发送OPTIONS请求检查CORS
        response = requests.options(
            "http://127.0.0.1:8000/api/faces/register/",
            headers={
                'Origin': 'http://localhost:3000',
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type,Authorization'
            },
            timeout=10
        )
        print(f"   OPTIONS请求状态码: {response.status_code}")
        print(f"   CORS头信息:")
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        for key, value in cors_headers.items():
            print(f"     {key}: {value}")
            
    except Exception as e:
        print(f"   ❌ CORS检查失败: {e}")

def generate_frontend_debug_code():
    """生成前端调试代码"""
    print("\n💻 生成前端调试代码...")
    
    debug_code = '''
// 在浏览器控制台中运行以下代码来调试前端问题

// 1. 检查当前状态
console.log("=== 前端调试信息 ===");
console.log("当前URL:", window.location.href);
console.log("Token:", localStorage.getItem('authToken'));

// 2. 检查网络请求
const originalFetch = window.fetch;
window.fetch = function(...args) {
    console.log("Fetch请求:", args);
    return originalFetch.apply(this, args).then(response => {
        console.log("Fetch响应:", response.status, response.statusText);
        return response;
    });
};

// 3. 检查axios请求（如果使用axios）
if (window.axios) {
    window.axios.interceptors.request.use(config => {
        console.log("Axios请求:", config);
        return config;
    });
    
    window.axios.interceptors.response.use(
        response => {
            console.log("Axios响应:", response.status, response.data);
            return response;
        },
        error => {
            console.log("Axios错误:", error);
            return Promise.reject(error);
        }
    );
}

// 4. 手动测试API调用
async function testFaceUpload() {
    const token = localStorage.getItem('authToken');
    if (!token) {
        console.error("没有找到Token");
        return;
    }
    
    try {
        const response = await fetch('/api/faces/', {
            headers: {
                'Authorization': `Token ${token}`
            }
        });
        console.log("获取人脸数据:", response.status, await response.json());
    } catch (error) {
        console.error("API调用失败:", error);
    }
}

// 运行测试
testFaceUpload();

console.log("=== 调试代码已加载 ===");
console.log("现在尝试上传图片，观察控制台输出");
'''
    
    print("   📋 请在浏览器开发者工具的Console标签中粘贴并运行以下代码:")
    print("   " + "="*60)
    print(debug_code)
    print("   " + "="*60)

def main():
    """主函数"""
    print("🚀 开始调试前端人脸上传问题")
    print("=" * 60)
    
    # 1. 检查用户状态
    token = check_user_status()
    
    if not token:
        print("\n❌ 用户状态检查失败，无法继续")
        return
    
    # 2. 测试API端点
    api_works = test_api_endpoint()
    
    # 3. 检查CORS
    check_cors_and_headers()
    
    # 4. 生成前端调试代码
    generate_frontend_debug_code()
    
    print("\n" + "=" * 60)
    print("📊 诊断总结:")
    print(f"   后端API工作: {'✅ 正常' if api_works else '❌ 异常'}")
    print(f"   用户Token: {'✅ 有效' if token else '❌ 无效'}")
    
    if api_works:
        print("\n🎯 后端工作正常，问题可能在前端:")
        print("   1. 打开浏览器开发者工具 (F12)")
        print("   2. 切换到Console标签")
        print("   3. 粘贴上面的调试代码并运行")
        print("   4. 尝试上传图片，观察控制台输出")
        print("   5. 检查Network标签是否有请求发出")
        print("   6. 检查是否有JavaScript错误")
    else:
        print("\n⚠️  后端API有问题，请先解决后端问题")

if __name__ == "__main__":
    main()
