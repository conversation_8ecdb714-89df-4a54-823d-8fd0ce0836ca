#!/usr/bin/env python
"""
Simple WebSocket Authentication Test

This script tests the WebSocket authentication middleware
without requiring a running server.

Usage:
    python test_websocket_simple.py
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).resolve().parent.parent  # Go up one level from test_script
sys.path.append(str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

try:
    import django
    django.setup()
    
    from django.contrib.auth.models import User
    from rest_framework.authtoken.models import Token
    from access_control.middleware import TokenAuthMiddleware
    from urllib.parse import urlencode
    
except ImportError as e:
    print(f"❌ Error importing required modules: {e}")
    sys.exit(1)


def test_token_authentication():
    """Test token authentication logic"""
    print("🧪 Testing Token Authentication Logic...")

    # Create test user and token
    user, created = User.objects.get_or_create(
        username='test_ws_user',
        defaults={'email': '<EMAIL>'}
    )
    token, created = Token.objects.get_or_create(user=user)

    # Create middleware instance
    middleware = TokenAuthMiddleware(None)

    # Test valid token (async function)
    import asyncio

    async def test_token_auth():
        user_from_token = await middleware.get_user_from_token(token.key)
        return user_from_token

    async def test_invalid_token_auth():
        invalid_user = await middleware.get_user_from_token('invalid_token_123')
        return invalid_user

    # Run async tests
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        user_from_token = loop.run_until_complete(test_token_auth())

        if user_from_token and user_from_token.id == user.id:
            print("✅ Valid token authentication works")
        else:
            print("❌ Valid token authentication failed")

        # Test invalid token
        invalid_user = loop.run_until_complete(test_invalid_token_auth())

        if invalid_user is None:
            print("✅ Invalid token correctly rejected")
        else:
            print("❌ Invalid token incorrectly accepted")

    finally:
        loop.close()


def test_device_authentication():
    """Test device authentication logic"""
    print("\n🤖 Testing Device Authentication Logic...")
    
    middleware = TokenAuthMiddleware(None)
    
    # Test valid device key
    device_id = "test_device_001"
    valid_key = f"device_{device_id}_secret_key"
    
    # This would normally be async, but we're testing the logic
    import asyncio
    
    async def test_device_auth():
        result = await middleware.authenticate_device(device_id, valid_key)
        return result
    
    # Run async test
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        valid_result = loop.run_until_complete(test_device_auth())
        if valid_result:
            print("✅ Valid device key authentication works")
        else:
            print("❌ Valid device key authentication failed")
        
        # Test invalid device key
        async def test_invalid_device_auth():
            result = await middleware.authenticate_device(device_id, "invalid_key")
            return result
        
        invalid_result = loop.run_until_complete(test_invalid_device_auth())
        if not invalid_result:
            print("✅ Invalid device key correctly rejected")
        else:
            print("❌ Invalid device key incorrectly accepted")
            
    finally:
        loop.close()


def test_scope_processing():
    """Test WebSocket scope processing"""
    print("\n🔍 Testing Scope Processing...")
    
    middleware = TokenAuthMiddleware(None)
    
    # Test device ID extraction
    test_scopes = [
        {
            'path': '/ws/device/esp32_001/',
            'expected': 'esp32_001'
        },
        {
            'path': '/ws/device/door_controller_01/',
            'expected': 'door_controller_01'
        },
        {
            'path': '/ws/admin/monitor/',
            'expected': None
        },
        {
            'path': '/invalid/path/',
            'expected': None
        }
    ]
    
    for test_case in test_scopes:
        device_id = middleware.extract_device_id_from_scope(test_case)
        expected = test_case['expected']
        
        if device_id == expected:
            print(f"✅ Path '{test_case['path']}' -> '{device_id}' (correct)")
        else:
            print(f"❌ Path '{test_case['path']}' -> '{device_id}' (expected '{expected}')")


def test_configuration():
    """Test WebSocket configuration"""
    print("\n⚙️  Testing WebSocket Configuration...")
    
    from django.conf import settings
    
    # Check ASGI application
    if hasattr(settings, 'ASGI_APPLICATION'):
        print(f"✅ ASGI_APPLICATION: {settings.ASGI_APPLICATION}")
    else:
        print("❌ ASGI_APPLICATION not configured")
    
    # Check Channel Layers
    if hasattr(settings, 'CHANNEL_LAYERS'):
        backend = settings.CHANNEL_LAYERS.get('default', {}).get('BACKEND', '')
        print(f"✅ Channel Layer Backend: {backend}")
        
        if 'InMemoryChannelLayer' in backend:
            print("⚠️  Note: Using InMemory layer (development only)")
        elif 'RedisChannelLayer' in backend:
            print("✅ Using Redis layer (production ready)")
    else:
        print("❌ CHANNEL_LAYERS not configured")
    
    # Check CORS settings
    if hasattr(settings, 'CORS_ALLOWED_ORIGINS'):
        origins = settings.CORS_ALLOWED_ORIGINS
        print(f"✅ CORS Origins: {len(origins)} configured")
        
        # Check for WebSocket support
        if hasattr(settings, 'CORS_ALLOW_WEBSOCKETS'):
            print(f"✅ WebSocket CORS: {settings.CORS_ALLOW_WEBSOCKETS}")
        else:
            print("⚠️  WebSocket CORS not explicitly configured")
    else:
        print("❌ CORS_ALLOWED_ORIGINS not configured")


def test_imports():
    """Test that all required modules can be imported"""
    print("\n📦 Testing Module Imports...")
    
    try:
        from access_control.consumers import DeviceConsumer, AdminMonitorConsumer, UserDashboardConsumer
        print("✅ WebSocket consumers imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import consumers: {e}")
    
    try:
        from access_control.middleware import TokenAuthMiddleware, DeviceKeyAuthMiddleware
        print("✅ Authentication middleware imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import middleware: {e}")
    
    try:
        import channels
        print(f"✅ Django Channels version: {channels.__version__}")
    except ImportError:
        print("❌ Django Channels not installed")
    
    try:
        import corsheaders
        print("✅ Django CORS Headers available")
    except ImportError:
        print("❌ Django CORS Headers not installed")


def main():
    """Run all tests"""
    print("🚀 WebSocket Authentication Configuration Test")
    print("=" * 60)
    
    test_imports()
    test_configuration()
    test_token_authentication()
    test_device_authentication()
    test_scope_processing()
    
    print("\n" + "=" * 60)
    print("✅ WebSocket authentication configuration test completed!")
    print("\n📝 Next steps:")
    print("1. Start the Django server: python manage.py runserver")
    print("2. Test WebSocket connections from frontend")
    print("3. Use browser dev tools to inspect WebSocket traffic")
    print("4. Check server logs for authentication events")


if __name__ == '__main__':
    main()
