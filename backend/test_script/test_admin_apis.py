#!/usr/bin/env python3
"""
管理员API接口测试脚本
测试所有管理员用户管理相关的API接口
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User

def test_admin_apis():
    """测试管理员API接口"""
    base_url = 'http://127.0.0.1:8000'
    
    print("🔧 开始测试管理员API接口...")
    print("=" * 60)
    
    # 1. 获取管理员token
    print("1️⃣ 测试管理员登录...")
    
    # 确保有管理员用户
    admin_users = User.objects.filter(is_superuser=True)
    if not admin_users.exists():
        print("❌ 没有找到管理员用户，请先创建管理员用户")
        return False
    
    admin_user = admin_users.first()
    print(f"   使用管理员用户: {admin_user.username}")
    
    # 尝试登录（假设密码是admin123）
    login_data = {
        'username': admin_user.username,
        'password': 'admin123'  # 默认密码
    }
    
    try:
        response = requests.post(f'{base_url}/api/auth/login/', json=login_data)
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"   ✅ 登录成功，Token: {token[:20]}...")
        else:
            print(f"   ❌ 登录失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 登录请求失败: {e}")
        return False
    
    headers = {'Authorization': f'Token {token}'}
    
    # 2. 测试用户列表API
    print("\n2️⃣ 测试用户列表API...")
    try:
        response = requests.get(f'{base_url}/api/admin/users/', headers=headers)
        if response.status_code == 200:
            data = response.json()
            user_count = len(data.get('results', []))
            total_count = data.get('count', 0)
            print(f"   ✅ 用户列表API正常: 当前页 {user_count} 个用户，总计 {total_count} 个用户")
            
            # 检查统计信息
            if 'stats' in data:
                stats = data['stats']
                print(f"   📊 统计信息: 总用户 {stats.get('total_users', 0)}, 活跃用户 {stats.get('active_users', 0)}")
        else:
            print(f"   ❌ 用户列表API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 用户列表API请求失败: {e}")
        return False
    
    # 3. 测试统计API
    print("\n3️⃣ 测试统计API...")
    try:
        response = requests.get(f'{base_url}/api/admin/users/stats/', headers=headers)
        if response.status_code == 200:
            stats = response.json()
            print(f"   ✅ 统计API正常:")
            print(f"      - 总用户: {stats.get('user_counts', {}).get('total', 0)}")
            print(f"      - 活跃用户: {stats.get('user_counts', {}).get('active', 0)}")
            print(f"      - 管理员: {stats.get('user_counts', {}).get('staff', 0)}")
            print(f"      - 本周新增: {stats.get('registration_trends', {}).get('new_this_week', 0)}")
        else:
            print(f"   ❌ 统计API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 统计API请求失败: {e}")
        return False
    
    # 4. 测试创建用户API
    print("\n4️⃣ 测试创建用户API...")
    test_user_data = {
        'username': f'testuser_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'email': '<EMAIL>',
        'password': 'testpass123',
        'confirm_password': 'testpass123',
        'full_name': '测试用户',
        'is_active': True,
        'is_staff': False,
        'is_superuser': False
    }
    
    try:
        response = requests.post(f'{base_url}/api/admin/users/create/', 
                               json=test_user_data, headers=headers)
        if response.status_code == 201:
            created_user = response.json()
            test_user_id = created_user['id']
            print(f"   ✅ 创建用户API正常: 创建用户 {created_user['username']} (ID: {test_user_id})")
        else:
            print(f"   ❌ 创建用户API失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 创建用户API请求失败: {e}")
        return False
    
    # 5. 测试用户详情API
    print("\n5️⃣ 测试用户详情API...")
    try:
        response = requests.get(f'{base_url}/api/admin/users/{test_user_id}/', headers=headers)
        if response.status_code == 200:
            user_detail = response.json()
            print(f"   ✅ 用户详情API正常: 获取用户 {user_detail['username']} 的详细信息")
        else:
            print(f"   ❌ 用户详情API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 用户详情API请求失败: {e}")
        return False
    
    # 6. 测试更新用户API
    print("\n6️⃣ 测试更新用户API...")
    update_data = {
        'username': test_user_data['username'],
        'email': '<EMAIL>',
        'full_name': '更新的测试用户',
        'is_active': True,
        'is_staff': False,
        'is_superuser': False
    }
    
    try:
        response = requests.put(f'{base_url}/api/admin/users/{test_user_id}/update/', 
                              json=update_data, headers=headers)
        if response.status_code == 200:
            updated_user = response.json()
            print(f"   ✅ 更新用户API正常: 更新用户邮箱为 {updated_user['email']}")
        else:
            print(f"   ❌ 更新用户API失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 更新用户API请求失败: {e}")
        return False
    
    # 7. 测试批量操作API
    print("\n7️⃣ 测试批量操作API...")
    batch_data = {
        'user_ids': [test_user_id],
        'action': 'deactivate'
    }
    
    try:
        response = requests.post(f'{base_url}/api/admin/users/batch/', 
                               json=batch_data, headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 批量操作API正常: {result['message']}, 成功 {result['success_count']} 个")
        else:
            print(f"   ❌ 批量操作API失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 批量操作API请求失败: {e}")
        return False
    
    # 8. 测试删除用户API
    print("\n8️⃣ 测试删除用户API...")
    try:
        response = requests.delete(f'{base_url}/api/admin/users/{test_user_id}/delete/', headers=headers)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 删除用户API正常: {result['message']}")
        else:
            print(f"   ❌ 删除用户API失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 删除用户API请求失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有管理员API接口测试通过！")
    return True

if __name__ == '__main__':
    success = test_admin_apis()
    if success:
        print("\n✅ 测试结果: 所有API接口正常工作")
    else:
        print("\n❌ 测试结果: 部分API接口存在问题")
        sys.exit(1)
