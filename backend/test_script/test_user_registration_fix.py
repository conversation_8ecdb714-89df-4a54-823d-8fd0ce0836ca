#!/usr/bin/env python
"""
测试用户注册修复脚本

这个脚本测试修复后的用户注册功能，确保不会出现Profile重复创建的问题。

使用方法:
    cd backend
    source venv/bin/activate
    python test_script/test_user_registration_fix.py
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from accounts.models import Profile
from django.db import transaction
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_user_registration():
    """测试用户注册功能"""
    print("🧪 测试用户注册修复...")
    
    # 测试用户名
    test_username = "test_user_fix_123"
    test_email = "<EMAIL>"
    test_password = "testpassword123"
    
    try:
        # 清理可能存在的测试用户
        if User.objects.filter(username=test_username).exists():
            User.objects.filter(username=test_username).delete()
            print(f"✅ 清理了已存在的测试用户: {test_username}")
        
        # 测试1: 直接创建用户（模拟Django信号）
        print("\n📝 测试1: 直接创建用户...")
        user = User.objects.create_user(
            username=test_username,
            email=test_email,
            password=test_password
        )
        
        # 检查Profile是否自动创建
        try:
            profile = Profile.objects.get(user=user)
            print(f"✅ Profile自动创建成功: {profile}")
        except Profile.DoesNotExist:
            print("❌ Profile未自动创建")
            return False
        
        # 清理测试用户
        user.delete()
        print("✅ 测试1通过")
        
        # 测试2: 使用序列化器创建用户（模拟API调用）
        print("\n📝 测试2: 使用序列化器创建用户...")
        from accounts.serializers import RegisterSerializer
        
        serializer_data = {
            'username': test_username,
            'email': test_email,
            'password': test_password
        }
        
        serializer = RegisterSerializer(data=serializer_data)
        if serializer.is_valid():
            user = serializer.save()
            
            # 检查Profile是否存在且唯一
            profile_count = Profile.objects.filter(user=user).count()
            if profile_count == 1:
                print(f"✅ Profile创建成功且唯一: {Profile.objects.get(user=user)}")
            elif profile_count == 0:
                print("❌ Profile未创建")
                return False
            else:
                print(f"❌ Profile重复创建: {profile_count}个")
                return False
        else:
            print(f"❌ 序列化器验证失败: {serializer.errors}")
            return False
        
        # 清理测试用户
        user.delete()
        print("✅ 测试2通过")
        
        # 测试3: 并发创建测试（模拟高并发场景）
        print("\n📝 测试3: 并发创建测试...")
        
        users_created = []
        for i in range(3):
            username = f"{test_username}_{i}"
            try:
                with transaction.atomic():
                    user = User.objects.create_user(
                        username=username,
                        email=f"test{i}@example.com",
                        password=test_password
                    )
                    users_created.append(user)
                    
                    # 检查Profile
                    profile_count = Profile.objects.filter(user=user).count()
                    if profile_count != 1:
                        print(f"❌ 用户{username}的Profile数量异常: {profile_count}")
                        return False
                    
            except Exception as e:
                print(f"❌ 创建用户{username}时出错: {e}")
                return False
        
        print(f"✅ 成功创建{len(users_created)}个用户，每个都有唯一的Profile")
        
        # 清理测试用户
        for user in users_created:
            user.delete()
        
        print("✅ 测试3通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_profile_signal_behavior():
    """测试Profile信号处理器行为"""
    print("\n🔧 测试Profile信号处理器行为...")
    
    test_username = "signal_test_user"
    
    try:
        # 清理
        if User.objects.filter(username=test_username).exists():
            User.objects.filter(username=test_username).delete()
        
        # 创建用户
        user = User.objects.create_user(
            username=test_username,
            password="testpass123"
        )
        
        # 检查初始Profile
        initial_profile = Profile.objects.get(user=user)
        print(f"✅ 初始Profile创建: {initial_profile}")
        
        # 更新用户信息（触发post_save信号）
        user.email = "<EMAIL>"
        user.save()
        
        # 检查Profile是否仍然唯一
        profile_count = Profile.objects.filter(user=user).count()
        if profile_count == 1:
            print("✅ 用户更新后Profile仍然唯一")
        else:
            print(f"❌ 用户更新后Profile数量异常: {profile_count}")
            return False
        
        # 清理
        user.delete()
        return True
        
    except Exception as e:
        print(f"❌ 信号测试失败: {e}")
        return False

def test_api_registration():
    """测试API注册功能（模拟前端调用）"""
    print("\n🌐 测试API注册功能...")

    import requests
    import json

    # API端点
    api_url = "http://127.0.0.1:8000/api/auth/register/"

    # 测试数据
    test_data = {
        "username": "api_test_user",
        "password": "testpassword123",
        "email": "<EMAIL>"
    }

    try:
        # 清理可能存在的用户
        if User.objects.filter(username=test_data["username"]).exists():
            User.objects.filter(username=test_data["username"]).delete()
            print(f"✅ 清理了已存在的测试用户: {test_data['username']}")

        # 发送POST请求
        print(f"📡 发送注册请求到: {api_url}")
        print(f"📝 请求数据: {test_data}")

        response = requests.post(
            api_url,
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )

        print(f"📊 响应状态码: {response.status_code}")
        print(f"📄 响应头: {dict(response.headers)}")

        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ 注册成功: {response_data}")

            # 验证用户是否创建
            user = User.objects.get(username=test_data["username"])
            profile = Profile.objects.get(user=user)
            print(f"✅ 用户创建成功: {user}")
            print(f"✅ Profile创建成功: {profile}")

            # 清理
            user.delete()
            return True

        else:
            print(f"❌ 注册失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"❌ 错误详情: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"❌ 响应内容: {response.text}")
            return False

    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Django服务器")
        print("💡 请确保Django服务器正在运行: python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_serializer_validation():
    """测试序列化器验证逻辑"""
    print("\n🔍 测试序列化器验证...")

    from accounts.serializers import RegisterSerializer

    # 测试用例
    test_cases = [
        {
            "name": "正常数据",
            "data": {"username": "valid_user", "password": "validpass123", "email": "<EMAIL>"},
            "should_pass": True
        },
        {
            "name": "缺少用户名",
            "data": {"password": "validpass123", "email": "<EMAIL>"},
            "should_pass": False
        },
        {
            "name": "缺少密码",
            "data": {"username": "valid_user", "email": "<EMAIL>"},
            "should_pass": False
        },
        {
            "name": "空用户名",
            "data": {"username": "", "password": "validpass123", "email": "<EMAIL>"},
            "should_pass": False
        },
        {
            "name": "空密码",
            "data": {"username": "valid_user", "password": "", "email": "<EMAIL>"},
            "should_pass": False
        },
        {
            "name": "无效邮箱",
            "data": {"username": "valid_user", "password": "validpass123", "email": "invalid-email"},
            "should_pass": False
        }
    ]

    all_passed = True

    for test_case in test_cases:
        print(f"\n📝 测试: {test_case['name']}")
        print(f"   数据: {test_case['data']}")

        serializer = RegisterSerializer(data=test_case['data'])
        is_valid = serializer.is_valid()

        if test_case['should_pass']:
            if is_valid:
                print(f"   ✅ 验证通过（预期）")
            else:
                print(f"   ❌ 验证失败（意外）: {serializer.errors}")
                all_passed = False
        else:
            if not is_valid:
                print(f"   ✅ 验证失败（预期）: {serializer.errors}")
            else:
                print(f"   ❌ 验证通过（意外）")
                all_passed = False

    return all_passed

def main():
    """主测试函数"""
    print("🚀 开始用户注册修复测试")
    print("=" * 50)

    # 运行测试
    tests = [
        ("用户注册功能", test_user_registration),
        ("Profile信号处理器", test_profile_signal_behavior),
        ("序列化器验证", test_serializer_validation),
        ("API注册功能", test_api_registration),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        if test_func():
            print(f"✅ {test_name} 测试通过")
            passed += 1
        else:
            print(f"❌ {test_name} 测试失败")

    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！用户注册bug已修复")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
