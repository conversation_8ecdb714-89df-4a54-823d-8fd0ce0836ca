#!/usr/bin/env python3
"""
测试欢迎信息显示功能
验证登录后localStorage存储和仪表盘显示是否正常工作
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User

def test_welcome_message_flow():
    """测试欢迎信息完整流程"""
    base_url = 'http://127.0.0.1:8000'
    
    print("🔧 开始测试欢迎信息显示功能...")
    print("=" * 60)
    
    # 1. 测试普通用户登录
    print("1️⃣ 测试普通用户登录...")
    
    login_data = {
        'username': 'test',
        'password': 'testtest'
    }
    
    try:
        response = requests.post(f'{base_url}/api/auth/login/', json=login_data)
        if response.status_code == 200:
            token_data = response.json()
            print(f"   ✅ 普通用户登录成功")
            print(f"   Token: {token_data['token'][:20]}...")
            print(f"   Username: {token_data['username']}")
            
            # 模拟前端localStorage存储
            welcome_message = f"欢迎回来，{token_data['username']}！"
            print(f"   📝 应该存储的欢迎信息: {welcome_message}")
            
        else:
            print(f"   ❌ 普通用户登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 普通用户登录请求失败: {e}")
        return False
    
    # 2. 测试管理员登录
    print("\n2️⃣ 测试管理员登录...")
    
    # 查找管理员用户
    try:
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            print("   ❌ 没有找到管理员用户")
            return False
            
        admin_login_data = {
            'username': admin_user.username,
            'password': 'admin123'  # 假设管理员密码
        }
        
        response = requests.post(f'{base_url}/api/auth/login/', json=admin_login_data)
        if response.status_code == 200:
            token_data = response.json()
            if token_data.get('is_staff'):
                print(f"   ✅ 管理员登录成功")
                print(f"   Token: {token_data['token'][:20]}...")
                print(f"   Username: {token_data['username']}")
                
                # 模拟前端localStorage存储
                admin_welcome_message = f"欢迎进入管理后台，{token_data['username']}！"
                print(f"   📝 应该存储的管理员欢迎信息: {admin_welcome_message}")
            else:
                print(f"   ❌ 用户不是管理员")
                return False
        else:
            print(f"   ❌ 管理员登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 管理员登录请求失败: {e}")
        return False
    
    # 3. 验证用户信息API
    print("\n3️⃣ 验证用户信息API...")
    
    try:
        headers = {'Authorization': f'Token {token_data["token"]}'}
        response = requests.get(f'{base_url}/api/auth/user/', headers=headers)
        if response.status_code == 200:
            user_data = response.json()
            print(f"   ✅ 用户信息API正常")
            print(f"   用户名: {user_data['username']}")
            print(f"   邮箱: {user_data.get('email', 'N/A')}")
            print(f"   是否管理员: {user_data.get('is_staff', False)}")
        else:
            print(f"   ❌ 用户信息API失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 用户信息API请求失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 欢迎信息测试完成！")
    print("\n📋 前端实现检查清单:")
    print("   ✅ 登录成功后存储localStorage('welcomeMessage')")
    print("   ✅ 登录成功后存储localStorage('adminWelcomeMessage')")
    print("   ✅ 仪表盘页面useEffect中检查localStorage")
    print("   ✅ 显示notification.success后清除localStorage")
    print("   ✅ 确保路由配置正确 (/dashboard, /admin/dashboard)")
    
    print("\n🔍 调试建议:")
    print("   1. 检查浏览器开发者工具 -> Application -> Local Storage")
    print("   2. 确认路由跳转是否正确到达仪表盘页面")
    print("   3. 检查浏览器控制台是否有JavaScript错误")
    print("   4. 确认notification组件是否正确导入和配置")
    
    return True

if __name__ == '__main__':
    success = test_welcome_message_flow()
    if success:
        print("\n✅ 测试结果: 后端API功能正常，请检查前端实现")
    else:
        print("\n❌ 测试结果: 后端API存在问题")
        sys.exit(1)
