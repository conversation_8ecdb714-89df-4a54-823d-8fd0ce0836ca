#!/usr/bin/env python3
"""
测试真实人脸图片上传

使用指定的真实人脸图片测试上传功能
"""

import os
import sys
import django
from pathlib import Path
import base64
import requests

# 设置Django环境
# 从test_script目录回到backend目录
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

def get_user_token(username='test1'):
    """获取用户Token"""
    print(f"🔑 获取用户 {username} 的Token...")
    
    try:
        user = User.objects.get(username=username)
        token, _ = Token.objects.get_or_create(user=user)
        print(f"   ✅ Token: {token.key[:10]}...")
        return token.key
    except User.DoesNotExist:
        print(f"   ❌ 用户 {username} 不存在")
        return None

def test_real_face_upload():
    """测试真实人脸图片上传"""
    print("🖼️  测试真实人脸图片上传...")
    
    # 指定的测试图片路径（现在脚本在test_script目录内）
    test_image_path = Path("test_face2.jpg")
    
    if not test_image_path.exists():
        print(f"   ❌ 测试图片不存在: {test_image_path}")
        print("   请确认图片路径是否正确")
        return False
    
    try:
        # 读取图片文件信息
        file_size = test_image_path.stat().st_size
        print(f"   📁 图片文件: {test_image_path}")
        print(f"   📏 文件大小: {file_size} bytes ({file_size/1024:.1f} KB)")
        
        # 读取图片并转换为base64
        with open(test_image_path, 'rb') as f:
            image_data = f.read()
        
        base64_image = "data:image/jpeg;base64," + base64.b64encode(image_data).decode()
        
        # 获取Token
        token = get_user_token()
        if not token:
            return False
        
        # 发送请求
        url = "http://127.0.0.1:8000/api/faces/register/"
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Token {token}',
        }
        data = {
            'image': base64_image
        }
        
        print(f"   🚀 发送请求到: {url}")
        print(f"   📤 Base64数据长度: {len(base64_image)} 字符")
        
        response = requests.post(url, json=data, headers=headers, timeout=60)
        
        print(f"   📥 响应状态码: {response.status_code}")
        
        if response.status_code in [200, 201]:
            print("   ✅ 真实人脸图片上传成功!")
            try:
                response_data = response.json()
                print(f"   📋 响应数据: {response_data}")
                
                # 检查是否是更新还是创建
                if response.status_code == 200:
                    print("   🔄 这是一次更新操作")
                else:
                    print("   🆕 这是一次创建操作")
                    
            except Exception as e:
                print(f"   📄 响应内容: {response.text}")
            return True
        else:
            print(f"   ❌ 上传失败")
            print(f"   📄 错误内容: {response.text}")
            
            # 分析错误类型
            if "Face could not be detected" in response.text:
                print("   💡 建议: 图片中可能没有检测到清晰的人脸")
            elif "Invalid base64" in response.text:
                print("   💡 建议: 图片格式或编码有问题")
            elif "401" in str(response.status_code):
                print("   💡 建议: 认证失败，检查Token")
            
            return False
            
    except FileNotFoundError:
        print(f"   ❌ 文件不存在: {test_image_path}")
        return False
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败 - Django服务器可能没有运行")
        print("   💡 请确保运行: python manage.py runserver")
        return False
    except Exception as e:
        print(f"   ❌ 测试异常: {e}")
        return False

def check_current_face_data():
    """检查当前用户的人脸数据"""
    print("\n🔍 检查当前人脸数据...")
    
    from face_db.models import FaceData
    
    try:
        user = User.objects.get(username='test1')
        try:
            face_data = user.face_data
            print(f"   ✅ 用户已有人脸数据:")
            print(f"     - ID: {face_data.id}")
            print(f"     - 创建时间: {face_data.created_at}")
            print(f"     - 更新时间: {face_data.updated_at}")
            print(f"     - 图片路径: {face_data.image_path}")
            print(f"     - Embedding长度: {len(face_data.embedding)} bytes")
            return True
        except FaceData.DoesNotExist:
            print("   ℹ️  用户暂无人脸数据")
            return False
    except User.DoesNotExist:
        print("   ❌ 用户test1不存在")
        return False

def main():
    """主函数"""
    print("🚀 开始测试真实人脸图片上传")
    print("=" * 60)
    
    # 检查当前人脸数据状态
    has_face_data = check_current_face_data()
    
    # 测试真实图片上传
    success = test_real_face_upload()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   上传前有人脸数据: {'✅ 是' if has_face_data else '❌ 否'}")
    print(f"   真实图片上传: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 修复成功！真实人脸图片上传正常工作")
        print("\n📋 下一步:")
        print("   1. 在前端测试人脸上传功能")
        print("   2. 检查浏览器开发者工具确认请求正常")
        print("   3. 验证人脸数据是否正确更新")
        
        # 再次检查人脸数据
        print("\n" + "=" * 60)
        check_current_face_data()
    else:
        print("\n⚠️  仍有问题，请检查:")
        print("   1. 图片是否包含清晰的人脸")
        print("   2. Django服务器是否正常运行")
        print("   3. 后端日志的详细错误信息")
        print("   4. DeepFace是否正确安装和配置")

if __name__ == "__main__":
    main()
