import requests
import base64
import os

# --- Configuration ---
BASE_URL = "http://127.0.0.1:8000"  # Your Django development server URL
LOGIN_URL = f"{BASE_URL}/api/auth/login/"
REGISTER_FACE_URL = f"{BASE_URL}/api/faces/register/"

# --- Test User Credentials ---
# IMPORTANT: Make sure this user exists in your database.
# You can create one via the registration page or createsuperuser.
TEST_USERNAME = "test1"
TEST_PASSWORD = "test1test1"

# --- Test Image ---
# IMPORTANT: Make sure this image file exists in the same directory as the script.
TEST_IMAGE_PATH = "test_face.jpg"


def get_auth_token(username, password):
    """Logs in a user and returns the authentication token."""
    print(f"Attempting to log in as user: {username}...")
    try:
        response = requests.post(LOGIN_URL, data={'username': username, 'password': password})
        response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
        
        data = response.json()
        token = data.get('token')
        
        if not token:
            print("Login failed: Token not found in response.")
            return None
            
        print("Login successful. Token obtained.")
        return token
    except requests.exceptions.RequestException as e:
        print(f"Login request failed: {e}")
        print(f"Response body: {e.response.text if e.response else 'No response'}")
        return None

def register_face(token, image_path):
    """Registers a face by sending a base64 encoded image."""
    if not os.path.exists(image_path):
        print(f"Error: Test image not found at '{image_path}'")
        return

    print(f"\nReading and encoding image: {image_path}...")
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
    
    # The backend expects a data URL format
    image_b64_data = f"data:image/jpeg;base64,{encoded_string}"

    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        'image': image_b64_data
    }

    print(f"Sending request to face registration API...")
    try:
        # Adding a timeout of 300 seconds (5 minutes) to handle potential model downloading
        response = requests.post(REGISTER_FACE_URL, headers=headers, json=payload, timeout=300)
        response.raise_for_status()
        
        print("Face registration request successful!")
        print("--- API Response ---")
        print(response.json())
        print("--------------------")
        
    except requests.exceptions.RequestException as e:
        print(f"Face registration request failed: {e}")
        print(f"Response status code: {e.response.status_code if e.response else 'N/A'}")
        print(f"Response body: {e.response.text if e.response else 'No response'}")


if __name__ == "__main__":
    # 1. Get auth token
    auth_token = get_auth_token(TEST_USERNAME, TEST_PASSWORD)
    
    # 2. If token is obtained, proceed to register face
    if auth_token:
        register_face(auth_token, TEST_IMAGE_PATH)
