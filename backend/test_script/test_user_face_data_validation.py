#!/usr/bin/env python
"""
test用户人脸数据验证测试脚本

专门用于验证test用户存储的人脸数据有效性：
1. 从数据库中提取test用户的人脸向量数据
2. 加载测试图片进行人脸特征提取
3. 计算余弦距离进行匹配验证
4. 输出详细的验证结果和数据分析

使用方法:
    cd backend
    source venv/bin/activate
    python test_script/test_user_face_data_validation.py

账号信息:
    用户名: test
    密码: testtest
"""

import os
import sys
import django
import numpy as np
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).resolve().parent.parent
sys.path.append(str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth.models import User
from face_db.models import FaceData
from face_db.services import represent_face
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 测试配置
TARGET_USERNAME = "test"
TEST_IMAGES = [
    "test_face.jpg",      # 测试图片1
    "test_face2.jpg",     # 测试图片2  
    "test_face3.jpg",     # 测试图片3
]

def extract_stored_face_data():
    """
    从数据库中提取test用户的人脸向量数据
    """
    print("🔍 正在提取存储的人脸数据...")
    
    try:
        # 获取test用户
        user = User.objects.get(username=TARGET_USERNAME)
        print(f"✅ 找到用户: {user.username} (ID: {user.id})")
        
        # 获取人脸数据
        face_data = FaceData.objects.get(user=user)
        print(f"✅ 找到人脸数据记录 (ID: {face_data.id})")
        
        # 转换存储的字节数据为numpy数组
        stored_embedding = np.frombuffer(face_data.embedding, dtype=np.float32)
        
        # 输出详细信息
        print(f"📊 人脸数据详情:")
        print(f"   - 创建时间: {face_data.created_at}")
        print(f"   - 更新时间: {face_data.updated_at}")
        print(f"   - 图片路径: {face_data.image_path}")
        print(f"   - 向量维度: {stored_embedding.shape}")
        print(f"   - 向量类型: {stored_embedding.dtype}")
        print(f"   - 向量范围: [{stored_embedding.min():.6f}, {stored_embedding.max():.6f}]")
        print(f"   - 向量均值: {stored_embedding.mean():.6f}")
        print(f"   - 向量标准差: {stored_embedding.std():.6f}")
        
        return user, face_data, stored_embedding
        
    except User.DoesNotExist:
        print(f"❌ 用户 '{TARGET_USERNAME}' 不存在")
        return None, None, None
    except FaceData.DoesNotExist:
        print(f"❌ 用户 '{TARGET_USERNAME}' 没有人脸数据")
        return None, None, None
    except Exception as e:
        print(f"❌ 提取人脸数据时出错: {e}")
        return None, None, None

def load_and_process_test_image(image_path):
    """
    加载并处理测试图片，提取人脸特征
    """
    print(f"🖼️  处理图片: {image_path.name}")
    
    if not image_path.exists():
        print(f"   ❌ 图片文件不存在")
        return None
    
    try:
        # 提取人脸特征
        print(f"   🔄 正在提取人脸特征...")
        embedding = represent_face(str(image_path))
        
        if embedding is None:
            print(f"   ❌ 未检测到人脸")
            return None
        
        print(f"   ✅ 成功提取人脸特征")
        print(f"   📊 特征向量维度: {embedding.shape}")
        print(f"   📊 特征向量范围: [{embedding.min():.6f}, {embedding.max():.6f}]")
        
        return embedding
        
    except Exception as e:
        print(f"   ❌ 处理图片时出错: {e}")
        return None

def calculate_face_similarity(stored_embedding, test_embedding):
    """
    计算两个人脸向量的相似度
    """
    try:
        # 计算余弦距离
        dot_product = np.dot(stored_embedding, test_embedding)
        norm_stored = np.linalg.norm(stored_embedding)
        norm_test = np.linalg.norm(test_embedding)
        
        cosine_similarity = dot_product / (norm_stored * norm_test)
        cosine_distance = 1 - cosine_similarity
        
        # 计算欧几里得距离
        euclidean_distance = np.linalg.norm(stored_embedding - test_embedding)
        
        return {
            'cosine_similarity': cosine_similarity,
            'cosine_distance': cosine_distance,
            'euclidean_distance': euclidean_distance,
            'dot_product': dot_product,
            'norm_stored': norm_stored,
            'norm_test': norm_test
        }
        
    except Exception as e:
        print(f"❌ 计算相似度时出错: {e}")
        return None

def run_face_matching_test(stored_embedding):
    """
    运行人脸匹配测试
    """
    print(f"\n🎯 开始人脸匹配测试...")
    
    script_dir = Path(__file__).parent
    results = []
    
    for i, image_name in enumerate(TEST_IMAGES, 1):
        print(f"\n--- 测试 {i}/{len(TEST_IMAGES)}: {image_name} ---")
        
        image_path = script_dir / image_name
        
        # 处理测试图片
        test_embedding = load_and_process_test_image(image_path)
        
        if test_embedding is None:
            results.append({
                'image': image_name,
                'success': False,
                'reason': '无法提取人脸特征'
            })
            continue
        
        # 计算相似度
        similarity_data = calculate_face_similarity(stored_embedding, test_embedding)
        
        if similarity_data is None:
            results.append({
                'image': image_name,
                'success': False,
                'reason': '相似度计算失败'
            })
            continue
        
        # 判断匹配结果
        threshold = 0.50  # VGG-Face模型的阈值
        is_match = similarity_data['cosine_distance'] <= threshold
        
        print(f"📊 相似度分析:")
        print(f"   - 余弦相似度: {similarity_data['cosine_similarity']:.6f}")
        print(f"   - 余弦距离: {similarity_data['cosine_distance']:.6f}")
        print(f"   - 欧几里得距离: {similarity_data['euclidean_distance']:.6f}")
        print(f"   - 匹配阈值: {threshold}")
        print(f"   - 匹配结果: {'✅ 匹配' if is_match else '❌ 不匹配'}")
        
        results.append({
            'image': image_name,
            'success': True,
            'is_match': is_match,
            'similarity_data': similarity_data,
            'threshold': threshold
        })
    
    return results

def print_test_summary(results):
    """
    打印测试结果汇总
    """
    print(f"\n" + "="*60)
    print(f"📋 测试结果汇总")
    print(f"="*60)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    matched_faces = sum(1 for r in results if r.get('is_match', False))
    
    print(f"总测试图片: {total_tests}")
    print(f"成功处理: {successful_tests}")
    print(f"匹配成功: {matched_faces}")
    print(f"匹配失败: {successful_tests - matched_faces}")
    
    print(f"\n📊 详细结果:")
    for result in results:
        image = result['image']
        if not result['success']:
            print(f"  {image}: ❌ {result['reason']}")
        else:
            if result['is_match']:
                distance = result['similarity_data']['cosine_distance']
                print(f"  {image}: ✅ 匹配 (距离: {distance:.4f})")
            else:
                distance = result['similarity_data']['cosine_distance']
                print(f"  {image}: ❌ 不匹配 (距离: {distance:.4f})")
    
    return matched_faces > 0

def main():
    """
    主函数
    """
    print("🚀 test用户人脸数据验证测试")
    print("="*60)
    
    # 1. 提取存储的人脸数据
    print(f"\n📋 步骤1: 提取存储的人脸数据")
    user, face_data, stored_embedding = extract_stored_face_data()
    
    if stored_embedding is None:
        print(f"\n❌ 无法获取人脸数据，测试终止")
        return False
    
    # 2. 运行人脸匹配测试
    print(f"\n📋 步骤2: 运行人脸匹配测试")
    results = run_face_matching_test(stored_embedding)
    
    # 3. 输出测试汇总
    success = print_test_summary(results)
    
    if success:
        print(f"\n🎉 测试完成！test用户的人脸数据有效且匹配功能正常")
    else:
        print(f"\n⚠️  测试完成，但人脸匹配存在问题，请检查:")
        print(f"   - 测试图片是否包含清晰的人脸")
        print(f"   - 测试图片是否与注册的人脸属于同一人")
        print(f"   - DeepFace模型是否正常工作")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print(f"\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
