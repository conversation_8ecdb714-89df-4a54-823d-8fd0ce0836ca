# Step 1: Add new fields

from django.db import migrations, models

class Migration(migrations.Migration):

    dependencies = [
        ('face_db', '0001_initial'),
    ]

    operations = [
        # Add updated_at field
        migrations.AddField(
            model_name='facedata',
            name='updated_at',
            field=models.DateTimeField(
                auto_now=True,
                help_text='When this face data was last updated',
                verbose_name='Updated At'
            ),
        ),
        
        # Update existing fields
        migrations.AlterField(
            model_name='facedata',
            name='embedding',
            field=models.BinaryField(
                help_text='Binary representation of the face embedding vector from DeepFace',
                verbose_name='Face Embedding Vector'
            ),
        ),
        
        migrations.AlterField(
            model_name='facedata',
            name='image_path',
            field=models.CharField(
                default='registered_via_api',
                help_text='Path to the registered face image for reference.',
                max_length=255
            ),
        ),
        
        migrations.AlterField(
            model_name='facedata',
            name='created_at',
            field=models.DateTimeField(
                auto_now_add=True,
                help_text='When this face data was first registered',
                verbose_name='Created At'
            ),
        ),
    ]
