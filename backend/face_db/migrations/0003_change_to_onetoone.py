# Step 2: Change to OneToOneField

from django.db import migrations, models
import django.db.models.deletion

class Migration(migrations.Migration):

    dependencies = [
        ('face_db', '0002_add_fields'),
    ]

    operations = [
        # Change user field to OneToOneField
        migrations.AlterField(
            model_name='facedata',
            name='user',
            field=models.OneToOneField(
                help_text='The user this face data belongs to (one-to-one relationship)',
                on_delete=django.db.models.deletion.CASCADE,
                related_name='face_data',
                to='auth.User',
                verbose_name='User'
            ),
        ),
        
        # Update model metadata
        migrations.AlterModelOptions(
            name='facedata',
            options={
                'ordering': ['-updated_at'],
                'verbose_name': 'Face Data',
                'verbose_name_plural': 'Face Data'
            },
        ),
    ]
