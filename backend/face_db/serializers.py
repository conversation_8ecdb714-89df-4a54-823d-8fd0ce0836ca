from rest_framework import serializers
from .models import FaceData

class FaceDataSerializer(serializers.ModelSerializer):
    class Meta:
        model = FaceData
        fields = ['id', 'user', 'image_path', 'created_at', 'updated_at']
        read_only_fields = ['user', 'created_at', 'updated_at']

class FaceRegistrationSerializer(serializers.Serializer):
    """
    Serializer for the face registration endpoint.
    Accepts a base64 encoded image string.
    """
    image = serializers.CharField(write_only=True)
