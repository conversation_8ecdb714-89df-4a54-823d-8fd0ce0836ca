from django.contrib.auth.models import User
from django.db import models

class FaceData(models.Model):
    """
    Stores face embedding data for a user.
    Each user can have only ONE face embedding for security and consistency.
    Uses OneToOneField to enforce database-level uniqueness constraint.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='face_data',
        verbose_name="User",
        help_text="The user this face data belongs to (one-to-one relationship)"
    )
    embedding = models.BinaryField(
        verbose_name="Face Embedding Vector",
        help_text="Binary representation of the face embedding vector from DeepFace"
    )
    image_path = models.Char<PERSON>ield(
        max_length=255,
        help_text="Path to the registered face image for reference.",
        default="registered_via_api"
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="Created At",
        help_text="When this face data was first registered"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="Updated At",
        help_text="When this face data was last updated"
    )

    class Meta:
        verbose_name = "Face Data"
        verbose_name_plural = "Face Data"
        ordering = ['-updated_at']

    def __str__(self):
        return f"Face data for {self.user.username} (updated: {self.updated_at.strftime('%Y-%m-%d %H:%M')})"
