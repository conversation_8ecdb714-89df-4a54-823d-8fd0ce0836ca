# 智能门禁管理系统 - 后端

本项目是智能门禁系统的后端部分，基于 **Django** 和 **Django Channels** 构建，负责处理业务逻辑、与硬件设备进行实时通信、并为前端提供 API 和 WebSocket 服务。

## ✨ 功能特性

-   **用户与认证**:
    -   基于 `djangorestframework` 的 Token 认证机制。
    -   区分普通用户和管理员权限。
    -   通过 `Profile` 模型扩展用户，关联 NFC 卡号等信息。
-   **双重门禁逻辑**:
    -   接收硬件上报的 NFC 刷卡事件。
    -   **调用摄像头服务捕获实时图像，并与预存的人脸数据进行 1:1 比对。**
    -   根据双重验证结果，向硬件发送开门或拒绝指令。
-   **实时通信**:
    -   使用 **Django Channels** 为硬件设备、前端管理员和普通用户提供独立的 WebSocket 端点。
    -   实时广播设备状态（在线/离线）、门禁事件等。
    -   **支持动态代理多个摄像头服务的视频流。**
-   **人脸识别集成**:
    -   通过 `deepface` 库实现人脸特征提取和比对。
    -   提供人脸数据注册和管理的 API。
-   **安全性**:
    -   通过 `.env` 文件管理敏感配置。
    -   提供生产环境的安全配置模板 (`.env.production.example`)。
    -   包含一个安全自检脚本 (`check_security.py`) 和部署检查脚本 (`deploy_check.sh`)。

## 🏛️ 项目结构

```
backend/
├── access_control/   # 核心门禁逻辑，包括WebSocket Consumers和日志模型
├── accounts/         # 用户账户、Profile模型和认证视图
├── face_db/          # 人脸数据模型、视图和Deepface服务封装
├── config/           # Django项目配置 (settings.py, urls.py, asgi.py)
├── test_script/      # 用于测试各类功能的独立脚本
├── manage.py         # Django 管理脚本
├── requirements.txt  # Python 依赖
└── README.md         # 本文档
```

## 🛠️ 技术栈

-   **核心框架**: Django, Django REST Framework
-   **实时通信**: Django Channels, Daphne
-   **人脸识别**: DeepFace
-   **数据库**: SQLite (开发), PostgreSQL (生产推荐)
-   **环境管理**: python-dotenv

## 🚀 快速开始

### 1. 环境配置

在运行项目之前，你需要配置必要的环境变量。

首先，将环境配置模板文件 `.env.example` 复制一份并重命名为 `.env`：

```bash
cp .env.example .env
```

然后，打开新建的 `.env` 文件，根据你的开发环境进行调整。最重要的配置项是：

-   `DJANGO_SECRET_KEY`: Django 的密钥，开发时可使用默认值。
-   `CORS_ALLOWED_ORIGINS`: 允许访问后端的的前端服务地址，例如 `http://localhost:5173`。
-   `CAMERA_SERVICES_JSON`: **（重要）** 用于配置一个或多个摄像头服务的JSON字符串。键是摄像头的唯一名称（如 `"main_gate_cam"`），值是该服务的URL。示例: `{"main_gate_cam": "http://*************:8080"}`。

### 2. 创建并激活虚拟环境

为了隔离项目依赖，强烈建议使用 Python 虚拟环境。

```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
# On macOS/Linux:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate
```

### 3. 安装依赖

激活虚拟环境后，使用 `pip` 安装 `requirements.txt` 中列出的所有依赖：

```bash
pip install -r requirements.txt
```
**注意**: `deepface` 库可能会有复杂的依赖项，特别是 `tensorflow`。请根据 `deepface` 的官方文档解决可能出现的安装问题。

### 4. 数据库迁移

首次运行时，需要创建数据库表结构：

```bash
python manage.py migrate
```

### 5. 创建超级用户

为了访问 Django Admin 和需要管理员权限的 API，你需要创建一个超级用户：

```bash
python manage.py createsuperuser
```

按照提示输入用户名、邮箱和密码。

### 6. 运行开发服务器

一切准备就绪后，启动 Django 开发服务器：

```bash
python manage.py runserver
```

服务器默认运行在 `http://127.0.0.1:8000/`。由于项目使用了 Django Channels，它会同时处理 HTTP 和 WebSocket 请求。

## ✅ 安全与部署检查

项目包含两个脚本来帮助你检查配置是否适合部署。

-   **`check_security.py`**: 一个 Python 脚本，用于检查 `settings.py` 中的常见安全问题，如 `DEBUG` 模式、`SECRET_KEY` 强度等。
    ```bash
    python check_security.py
    ```

-   **`deploy_check.sh`**: 一个 Shell 脚本，它会运行 `check_security.py` 和 Django 内置的部署检查命令 `python manage.py check --deploy`。
    ```bash
    # 确保脚本有执行权限
    chmod +x deploy_check.sh

    # 运行检查
    ./deploy_check.sh production
    ```

在将项目部署到生产环境之前，请务必运行这些检查并修复所有报告的问题。