# Django Environment Variables
# Copy this file to .env and fill in the values.

# SECURITY WARNING: In a real production environment, generate a strong, unique secret key.
# You can generate one using: python -c 'from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())'
DJANGO_SECRET_KEY=your-super-secret-key-here

# Set to False in production
DJANGO_DEBUG=True

# Comma-separated list of allowed hosts. For example: .example.com,localhost,127.0.0.1
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1

# --- Camera Service Configuration for WSL2 Development ---
#
# To allow the Django backend (running in WSL2) to access the camera service (running on Windows),
# you must use the IP address of your Windows host machine as seen from within WSL2.
#
# HOW TO FIND THE CORRECT IP:
# 1. Open a WSL2 terminal.
# 2. Run the following command:
#    cat /etc/resolv.conf | grep nameserver | awk '{print $2}'
# 3. This command will output an IP address. This is the IP of your Windows host.
#
# Replace "YOUR_WINDOWS_HOST_IP_IN_WSL" in the URL below with the IP you found.
#
# Example:
# If the command outputs "************", your JSON should look like this:
# CAMERA_SERVICES_JSON={"camera_1": "http://************:8080"}

CAMERA_SERVICES_JSON={"camera_1": "http://YOUR_WINDOWS_HOST_IP_IN_WSL:8080"}

# --- CORS Configuration ---
# Comma-separated list of frontend origins allowed to connect.
# Default allows standard Vite and Create React App dev servers.
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://127.0.0.1:5173,http://localhost:3000,http://127.0.0.1:3000

# --- Internal Communication Secret ---
# A secret key shared between the Django backend and other internal services (like the camera service).
# This is used for simple authentication on internal-only APIs.
# You can generate a random string for this.
INTERNAL_SECRET_KEY=a-very-strong-and-random-secret-for-internal-use
