from django.urls import path
from .views import (
    # 用户认证相关
    RegisterAPI, LoginAPI, UserAPI, UserProfileUpdateAPI,
    UserSelfProfileUpdateAPI, ChangePasswordAPI, UpdateUsernameAPI,
    # 管理员用户管理相关
    AdminUserListAPI, AdminUserCreateAPI, AdminUserDetailAPI,
    AdminUserUpdateAPI, AdminUserDeleteAPI, AdminUserBatchAPI, AdminUserStatsAPI
)

urlpatterns = [
    # ==================== 用户认证API ====================
    path('auth/register/', RegisterAPI.as_view(), name='register'),
    path('auth/login/', LoginAPI.as_view(), name='login'),
    path('auth/user/', UserAPI.as_view(), name='user'),
    path('auth/profile/', UserSelfProfileUpdateAPI.as_view(), name='user-self-profile-update'),
    path('auth/change-password/', ChangePasswordAPI.as_view(), name='change-password'),
    path('auth/update-username/', UpdateUsernameAPI.as_view(), name='update-username'),
    path('users/<int:pk>/profile/', UserProfileUpdateAPI.as_view(), name='user-profile-update'),

    # ==================== 管理员用户管理API ====================
    # 用户列表和创建
    path('admin/users/', AdminUserListAPI.as_view(), name='admin-user-list'),
    path('admin/users/create/', AdminUserCreateAPI.as_view(), name='admin-user-create'),

    # 用户详情、更新和删除
    path('admin/users/<int:pk>/', AdminUserDetailAPI.as_view(), name='admin-user-detail'),
    path('admin/users/<int:pk>/update/', AdminUserUpdateAPI.as_view(), name='admin-user-update'),
    path('admin/users/<int:pk>/delete/', AdminUserDeleteAPI.as_view(), name='admin-user-delete'),

    # 批量操作和统计
    path('admin/users/batch/', AdminUserBatchAPI.as_view(), name='admin-user-batch'),
    path('admin/users/stats/', AdminUserStatsAPI.as_view(), name='admin-user-stats'),
]