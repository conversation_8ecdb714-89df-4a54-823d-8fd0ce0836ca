from rest_framework import serializers
from .models import AccessLog, Device

class AccessLogSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField()
    device = serializers.StringRelatedField()

    class Meta:
        model = AccessLog
        fields = '__all__'

class DeviceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Device
        fields = '__all__'