# Generated by Django 4.2.23 on 2025-07-14 14:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('access_control', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Device',
            fields=[
                ('device_id', models.CharField(help_text='Unique identifier for the device (e.g., ESP32_001)', max_length=100, primary_key=True, serialize=False, unique=True)),
                ('name', models.Char<PERSON>ield(help_text='Human-readable name for the device', max_length=100)),
                ('location', models.CharField(blank=True, help_text='Physical location of the device', max_length=255)),
                ('status', models.CharField(choices=[('online', 'Online'), ('offline', 'Offline'), ('error', 'Error')], default='offline', max_length=20, verbose_name='Device Status')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('firmware_version', models.CharField(blank=True, max_length=50, verbose_name='Firmware Version')),
                ('last_seen', models.DateTimeField(blank=True, null=True, verbose_name='Last Seen')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Date Added')),
            ],
            options={
                'verbose_name': 'Device',
                'verbose_name_plural': 'Devices',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='accesslog',
            name='device',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='access_logs', to='access_control.device', verbose_name='Device'),
        ),
    ]
