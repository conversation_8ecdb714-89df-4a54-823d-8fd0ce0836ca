# Generated by Django 4.2.23 on 2025-07-17 07:33

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('access_control', '0003_accesslog_failure_reason'),
    ]

    operations = [
        migrations.AddField(
            model_name='device',
            name='camera_service_name',
            field=models.CharField(blank=True, help_text="The name of the camera service associated with this device (e.g., 'main_gate_cam'). This name is used as a key in settings.CAMERA_SERVICES.", max_length=100),
        ),
    ]
