# Generated by Django 4.2.23 on 2025-07-19 08:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('access_control', '0004_device_camera_service_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='VideoStreamToken',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_used', models.BooleanField(default=False)),
                ('device', models.ForeignKey(help_text='The device stream being accessed.', on_delete=django.db.models.deletion.CASCADE, to='access_control.device')),
                ('user', models.ForeignKey(help_text='The admin user who requested the stream.', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
