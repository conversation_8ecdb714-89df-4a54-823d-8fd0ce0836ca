from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # Device connections (ESP32, door controllers, etc.)
    re_path(r'ws/device/(?P<device_id>\w+)/$', consumers.DeviceConsumer.as_asgi()),

    # Admin monitoring connections
    re_path(r'ws/admin/monitor/$', consumers.AdminMonitorConsumer.as_asgi()),

    # User dashboard connections
    re_path(r'ws/user/dashboard/$', consumers.UserDashboardConsumer.as_asgi()),
]