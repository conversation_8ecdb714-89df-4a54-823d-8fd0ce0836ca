from django.urls import path
from .views import (
    AccessLogListAPI,
    AccessLogDestroyAPI,
    AccessLogBatchDeleteAPI,
    DeviceListAPIView,
    BindCameraToDeviceAPIView,
    CameraServiceListAPIView,
    GenerateVideoStreamURL,
    VerifyVideoStreamToken,
)

urlpatterns = [
    # Public-facing APIs for admin frontend
    path('admin/camera_services/', CameraServiceListAPIView.as_view(), name='admin-camera-service-list'),
    path('admin/devices/', DeviceListAPIView.as_view(), name='admin-device-list'),
    path('admin/devices/<str:device_id>/bind_camera/', BindCameraToDeviceAPIView.as_view(), name='admin-device-bind-camera'),
    path('admin/devices/<str:device_id>/stream_url/', GenerateVideoStreamURL.as_view(), name='admin-device-stream-url'),
    path('admin/logs/', AccessLogListAPI.as_view(), name='admin-log-list'),
    path('admin/logs/<int:pk>/', AccessLogDestroyAPI.as_view(), name='admin-log-delete'),
    path('admin/logs/batch-delete/', AccessLogBatchDeleteAPI.as_view(), name='admin-log-batch-delete'),
    
    # Internal API for camera service to verify tokens
    path('internal/verify_stream_token/', VerifyVideoStreamToken.as_view(), name='internal-verify-stream-token'),
]