 /*
  * ESP32智能门禁设备配置模板
  * 
  * 使用说明：
  * 1. 复制此文件为 config.h
  * 2. 修改下面的配置参数
  * 3. 在主代码中包含 config.h 而不是直接修改主代码
  */
 
  #ifndef CONFIG_H
  #define CONFIG_H
  
  // ==================== WiFi配置 ====================
  // 请修改为您的WiFi网络信息
  #define WIFI_SSID "YOUR_WIFI_SSID"
  #define WIFI_PASSWORD "YOUR_WIFI_PASSWORD"
  
  // ==================== 服务器配置 ====================
  // 请修改为您的服务器信息
  #define WEBSOCKET_SERVER "YOUR_SERVER_IP"  // 服务器IP地址
  #define WEBSOCKET_PORT 8000               // WebSocket端口
  #define WEBSOCKET_PATH "/ws/device/ESP32_001/"  // WebSocket路径
  
  // ==================== 设备配置 ====================
  // 设备唯一标识信息
  #define DEVICE_ID "ESP32_001"
  #define DEVICE_NAME "主入口门禁"
  #define DEVICE_LOCATION "办公楼主入口"
  #define DEVICE_KEY "device_ESP32_001_secret_key"  // 设备密钥，必须与后端规则匹配
  #define FIRMWARE_VERSION "v1.0.0"
  
  // ==================== 硬件引脚配置 ====================
  // NFC模块引脚 - 使用用户现有引脚的最优方案
  #define NFC_RST_PIN 2        // 复位引脚 (G2)
  #define NFC_SS_PIN 5         // 片选引脚 (G5)
  
  // 输出设备引脚
  #define BUZZER_PIN 18
  #define LED_GREEN_PIN 19
  #define LED_RED_PIN 23
  #define RELAY_PIN 25
  
  // 输入设备引脚  
  #define BUTTON_PIN 26
  
  // 可选硬件引脚
  #define DOOR_SENSOR_PIN 27    // 门磁传感器（可选）
  #define CAMERA_TRIGGER_PIN 32 // 摄像头触发（可选）
  
  // ==================== 时间配置 ====================
  // 各种时间间隔设置（毫秒）
  #define HEARTBEAT_INTERVAL 30000    // 心跳间隔：30秒
  #define NFC_CHECK_INTERVAL 500      // NFC检查间隔：0.5秒
  #define DOOR_AUTO_CLOSE_DELAY 5000  // 自动关门延时：5秒
  #define BUTTON_DEBOUNCE_DELAY 50    // 按钮防抖延时：50毫秒
  #define CARD_DUPLICATE_TIMEOUT 2000 // 防重复读卡超时：2秒
  
  // ==================== 网络配置 ====================
  // WiFi连接参数
  #define WIFI_CONNECT_TIMEOUT 20     // WiFi连接超时（秒）
  #define WIFI_RETRY_INTERVAL 5000    // WiFi重连间隔（毫秒）
  
  // WebSocket参数
  #define WS_RECONNECT_INTERVAL 5000  // WebSocket重连间隔（毫秒）
  #define WS_PING_INTERVAL 25000      // WebSocket ping间隔（毫秒）
  
  // ==================== 功能开关 ====================
  // 启用/禁用特定功能
  #define ENABLE_NFC true             // 启用NFC功能
  #define ENABLE_BUZZER true          // 启用蜂鸣器
  #define ENABLE_LED_INDICATOR true   // 启用LED指示
  #define ENABLE_MANUAL_BUTTON true   // 启用手动按钮
  #define ENABLE_DOOR_SENSOR false    // 启用门磁传感器（可选）
  #define ENABLE_CAMERA_TRIGGER false // 启用摄像头触发（可选）
  
  // 调试选项
  #define ENABLE_SERIAL_DEBUG true    // 启用串口调试
  #define ENABLE_VERBOSE_LOG false    // 启用详细日志
  
  // ==================== 安全配置 ====================
  // 安全相关设置
  #define MAX_FAILED_ATTEMPTS 5       // 最大失败尝试次数
  #define LOCKOUT_DURATION 300000     // 锁定持续时间（毫秒）：5分钟
  #define ENABLE_DEVICE_AUTH true     // 启用设备认证
  
  // ==================== 高级配置 ====================
  // 内存和性能设置
  #define JSON_BUFFER_SIZE 1024       // JSON缓冲区大小
  #define SERIAL_BAUD_RATE 115200     // 串口波特率
  #define SPI_SPEED 1000000           // SPI通信速度
  
  // NFC特定设置
  #define NFC_ANTENNA_GAIN 0x07       // NFC天线增益
  #define NFC_TIMEOUT 50              // NFC操作超时（毫秒）
  
  // ==================== 协议事件定义 ====================
  // C->S (客户端到服务器)
  #define EVENT_DEVICE_REGISTER "device_register"
  #define EVENT_HEARTBEAT "heartbeat"
  #define EVENT_NFC_SCAN "nfc_scan"
  #define EVENT_DEVICE_STATUS "device_status"
  
  // S->C (服务器到客户端)
  #define EVENT_CONNECTION_ESTABLISHED "connection_established"
  #define EVENT_DEVICE_REGISTER_ACK "device_register_ack"
  #define EVENT_HEARTBEAT_RESPONSE "heartbeat_response"
  #define EVENT_DEVICE_RESTART "device_restart"
  #define EVENT_STATUS_REQUEST "status_request"
  #define EVENT_ACCESS_GRANTED "access_granted"
  #define EVENT_ACCESS_DENIED "access_denied"
  
  // ==================== 预定义消息 ====================
  // 系统消息定义
  #define MSG_WIFI_CONNECTING "正在连接WiFi..."
  #define MSG_WIFI_CONNECTED "WiFi连接成功"
  #define MSG_WIFI_FAILED "WiFi连接失败"
  #define MSG_WS_CONNECTING "正在连接WebSocket..."
  #define MSG_WS_CONNECTED "WebSocket连接成功"
  #define MSG_WS_DISCONNECTED "WebSocket连接断开"
  #define MSG_DEVICE_READY "设备就绪"
  #define MSG_NFC_DETECTED "检测到NFC卡"
  #define MSG_DOOR_OPENED "门已开启"
  #define MSG_DOOR_CLOSED "门已关闭"
  #define MSG_MANUAL_OPEN "手动开门"
  
  // ==================== 错误代码 ====================
  // 系统错误代码定义
  #define ERROR_WIFI_TIMEOUT 1001
  #define ERROR_WS_CONNECTION 1002
  #define ERROR_NFC_INIT 1003
  #define ERROR_HARDWARE_FAULT 1004
  #define ERROR_AUTH_FAILED 1005
  
  #endif // CONFIG_H
