# ESP32智能门禁系统 V2.0 - Arduino代码

## 🆕 V0.28.1 更新内容

- ✅ **新增OLED显示功能**
- ✅ **新增继电器控制功能**  
- ✅ **移除蜂鸣器和摄像头触发功能**
- ✅ **优化用户界面和状态显示**
- ✅ **改进硬件引脚配置**

## 功能特性

- 🔌 **WiFi连接**: 自动连接到指定WiFi网络
- 🌐 **WebSocket通信**: 与后端服务器实时通信
- 🏷️ **NFC读卡**: 支持MIFARE Classic等NFC卡片
- 📺 **OLED显示**: 实时显示设备状态和操作信息
- 🔓 **继电器控制**: 自动门锁控制
- 💡 **LED指示**: 绿/红LED状态指示
- 🌈 **RGB状态灯**: 彩色状态指示（绿色=开门，黄色=关门，红色=失败）
- 🔘 **手动按钮**: 紧急手动开门功能
- 💾 **状态保存**: 设备注册状态持久化存储

## 硬件要求

### 主控制器
- **ESP32开发板** (任何标准ESP32开发板)

### 外设模块
- **RC522 NFC模块** - NFC卡片读取
- **SSD1306 OLED显示屏** (128x64) - 状态显示
- **继电器模块** - 门锁控制
- **LED指示灯** (绿色/红色) - 状态指示
- **RGB LED** (WS2812B/NeoPixel) - 彩色状态指示
- **按钮开关** - 手动开门

## 引脚配置

| 功能 | 引脚 | 说明 |
|------|------|------|
| **NFC模块** | | |
| RST | GPIO2 | 复位引脚 |
| SDA/SS | GPIO5 | 片选引脚 |
| SCK | GPIO18 | SPI时钟 |
| MOSI | GPIO23 | SPI数据输出 |
| MISO | GPIO19 | SPI数据输入 |
| **OLED显示** | | |
| SDA | GPIO42 | I2C数据线 |
| SCL | GPIO41 | I2C时钟线 |
| **输出控制** | | |
| 继电器 | GPIO14 | 门锁控制 |
| 绿色LED | GPIO19 | 成功指示 |
| 红色LED | GPIO23 | 失败指示 |
| RGB LED | GPIO48 | 彩色状态指示 |
| **输入控制** | | |
| 手动按钮 | GPIO26 | 紧急开门 |

## OLED显示内容

### 主界面显示
```
主入口门禁
================
设备就绪
----------------
状态: 已连接
```

### 状态信息显示
- **设备名称**: 显示在顶部
- **当前状态**: WiFi连接、服务器连接状态
- **操作信息**: 刷卡检测、访问结果等
- **临时消息**: 3秒后自动返回主界面

## 继电器控制逻辑

### 访问授权时
1. 继电器激活 (GPIO14输出HIGH)
2. 绿色LED点亮
3. RGB LED显示绿色（门开启状态）
4. OLED显示"访问授权"和用户信息
5. 3秒后自动关闭继电器
6. RGB LED变为黄色（门关闭状态）
7. LED熄灭，返回主界面

### 手动开门
1. 按下手动按钮 (GPIO26)
2. 继电器激活3秒
3. RGB LED显示绿色（门开启状态）
4. OLED显示"手动开门"
5. 3秒后RGB LED变为黄色（门关闭状态）

## 快速开始

### 1. 安装依赖库

在Arduino IDE中安装以下库：
- **WebSockets** by Markus Sattler
- **ArduinoJson** by Benoit Blanchon
- **MFRC522** by GithubCommunity
- **Adafruit SSD1306** by Adafruit
- **Adafruit GFX Library** by Adafruit
- **Adafruit NeoPixel** by Adafruit

详细安装说明请参见 `libraries.txt`

### 2. 配置设备

编辑 `config.h` 文件，修改以下配置：

```cpp
// WiFi配置
#define WIFI_SSID "您的WiFi名称"
#define WIFI_PASSWORD "您的WiFi密码"

// 服务器配置  
#define WEBSOCKET_SERVER "192.168.1.3"
#define WEBSOCKET_PORT 8000

// 设备配置
#define DEVICE_ID "ESP32_001"
#define DEVICE_NAME "主入口门禁"

// OLED配置
#define OLED_SDA_PIN 42
#define OLED_SCL_PIN 41

// 继电器配置
#define RELAY_PIN 14

// RGB LED配置
#define RGB_LED_PIN 48
```

### 3. 硬件连接

按照引脚配置表连接所有硬件模块

### 4. 上传代码

1. 在Arduino IDE中打开 `access_control_device.ino`
2. 选择正确的开发板（ESP32 Dev Module）
3. 选择正确的串口
4. 点击上传

## 使用流程

### 设备启动
1. **硬件初始化**: LED、继电器、按钮引脚
2. **OLED初始化**: 显示设备名称和启动信息
3. **WiFi连接**: 连接到配置的WiFi网络
4. **WebSocket连接**: 连接到后端服务器
5. **NFC初始化**: 初始化NFC读卡模块
6. **设备就绪**: 显示就绪状态，等待刷卡

### 正常操作
1. **刷卡检测**: 将NFC卡片靠近读卡器
2. **OLED显示**: 显示"检测到卡片"和卡片UID
3. **服务器验证**: 发送卡片信息到后端验证
4. **结果显示**:
   - 成功: 绿灯亮，RGB LED绿色，继电器开门，显示欢迎信息
   - 失败: 红灯亮，RGB LED红色，显示拒绝原因
5. **自动复位**: 3秒后RGB LED变黄色，返回就绪状态

### 紧急开门
1. 按下手动按钮 (GPIO26)
2. 继电器立即激活3秒
3. RGB LED显示绿色
4. OLED显示"手动开门"
5. 3秒后RGB LED变黄色，自动返回正常状态

## 状态指示

### LED指示
- **绿灯**: 访问授权成功
- **红灯**: 访问被拒绝
- **全灭**: 正常待机状态

### RGB LED状态指示
- **🟢 绿色**: 门开启状态（访问授权/手动开门）
- **🟡 黄色**: 门关闭状态（正常待机）
- **🔴 红色**: 验证失败状态
- **🔵 蓝色**: 系统状态（可扩展）

### OLED显示状态
- **连接WiFi...**: WiFi连接中
- **连接服务器...**: WebSocket连接中
- **设备就绪**: 正常工作状态
- **检测到卡片**: NFC卡片检测
- **访问授权**: 验证成功
- **访问拒绝**: 验证失败

## 故障排除

### 常见问题

#### OLED不显示
- 检查I2C连接 (SDA: GPIO42, SCL: GPIO41)
- 验证OLED地址 (通常是0x3C)
- 确认电源连接

#### 继电器不工作
- 检查GPIO14连接
- 验证继电器模块电源
- 确认继电器触发电平设置

#### RGB LED不亮或颜色错误
- 检查GPIO48连接
- 验证RGB LED电源（5V或3.3V）
- 确认LED类型（WS2812B/NeoPixel）
- 检查数据线连接

#### NFC读卡失败
- 检查SPI连接
- 验证RC522模块电源
- 确保卡片距离适当

#### WiFi连接失败
- 检查SSID和密码配置
- 确认WiFi信号强度
- 验证网络可用性

### 调试方法
1. **串口监视器**: 查看详细的运行日志
2. **OLED显示**: 观察实时状态信息
3. **LED指示**: 检查硬件工作状态

## 技术规格

- **处理器**: ESP32 (双核，240MHz)
- **内存**: 520KB SRAM
- **存储**: 4MB Flash
- **通信**: WiFi 802.11 b/g/n
- **接口**: SPI, I2C, GPIO
- **工作电压**: 3.3V
- **工作温度**: -40°C ~ +85°C

## 版本信息

- **当前版本**: V0.28.1
- **兼容性**: ESP32 Arduino Core 2.0.0+
- **测试环境**: Arduino IDE 1.8.19, ESP32 Dev Module

这个V2.0版本提供了完整的OLED显示和继电器控制功能，为智能门禁系统提供了更好的用户体验和硬件控制能力。
