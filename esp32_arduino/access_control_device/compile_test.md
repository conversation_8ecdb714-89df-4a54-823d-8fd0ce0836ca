# ESP32 Arduino代码编译测试指南

## 编译前检查清单

### 1. 必需的Arduino库
确保已安装以下库：

```
- WebSockets by <PERSON> (v2.3.6+)
- <PERSON><PERSON><PERSON><PERSON><PERSON> by <PERSON><PERSON> (v6.19.4+)
- MFRC522 by GithubCommunity (v1.4.10+)
- Adafruit SSD1306 by Adafruit (v2.5.7+)
- Adafruit GFX Library by Adafruit (v1.11.3+)
- Adafruit NeoPixel by Adafruit (v1.10.7+)
```

### 2. 开发板设置
- **开发板**: ESP32 Dev Module
- **CPU频率**: 240MHz (WiFi/BT)
- **Flash频率**: 80MHz
- **Flash模式**: QIO
- **Flash大小**: 4MB (32Mb)
- **分区方案**: Default 4MB with spiffs

### 3. 配置文件检查
确保 `config.h` 文件存在并包含正确的配置：

```cpp
// WiFi配置
#define WIFI_SSID "你的WiFi名称"
#define WIFI_PASSWORD "你的WiFi密码"

// 服务器配置
#define WEBSOCKET_SERVER "192.168.1.3"
#define WEBSOCKET_PORT 8000

// 设备配置
#define DEVICE_ID "ESP32_001"
#define DEVICE_NAME "主入口门禁"

// 硬件引脚配置
#define RGB_LED_PIN 48
#define RELAY_PIN 14
#define OLED_SDA_PIN 42
#define OLED_SCL_PIN 41
```

## 常见编译错误及解决方案

### 1. 库未找到错误
```
fatal error: Adafruit_NeoPixel.h: No such file or directory
```
**解决方案**: 在Arduino IDE库管理器中安装缺失的库

### 2. 函数未声明错误
```
'initializeRgbLed' was not declared in this scope
```
**解决方案**: 确保函数声明在文件顶部正确添加

### 3. 引脚冲突错误
```
GPIO48 is not available on this board
```
**解决方案**: 检查ESP32开发板型号，确认GPIO48可用

### 4. 内存不足错误
```
Sketch too big
```
**解决方案**: 
- 选择更大的分区方案
- 优化代码减少内存使用

## 编译步骤

1. **打开Arduino IDE**
2. **选择正确的开发板和端口**
3. **打开 `access_control_device.ino`**
4. **点击验证按钮** (✓)
5. **检查编译输出**

## 成功编译的输出示例

```
正在编译项目...
项目使用了 XXXXX 字节，占用了 (XX%) 程序存储空间。最大为 1310720 字节。
全局变量使用了 XXXXX 字节，占用了 (XX%) 的动态内存，剩余 XXXXX 字节用于局部变量。最大为 327680 字节。
```

## 上传前的最终检查

- [ ] 所有库已正确安装
- [ ] config.h文件配置正确
- [ ] 开发板设置正确
- [ ] 串口端口选择正确
- [ ] 编译无错误无警告

## 硬件连接验证

上传代码前，确保硬件连接正确：

### RGB LED (WS2812B)
- VCC → 5V 或 3.3V
- GND → GND  
- DIN → GPIO48

### OLED显示屏
- VCC → 3.3V
- GND → GND
- SDA → GPIO42
- SCL → GPIO41

### 继电器模块
- VCC → 5V
- GND → GND
- IN → GPIO14

### NFC模块 (RC522)
- VCC → 3.3V
- GND → GND
- RST → GPIO2
- SDA → GPIO5
- SCK → GPIO18
- MOSI → GPIO23
- MISO → GPIO19

## 调试建议

1. **串口监视器**: 波特率设置为115200
2. **逐步测试**: 先测试基本功能，再添加复杂功能
3. **硬件测试**: 使用 `testRgbLed()` 函数测试RGB LED
4. **日志输出**: 观察串口输出的初始化信息

## 故障排除

如果编译仍然失败：

1. **重启Arduino IDE**
2. **清理编译缓存**: 工具 → 清理编译缓存
3. **重新安装库**: 删除并重新安装有问题的库
4. **检查ESP32核心版本**: 确保使用兼容的ESP32 Arduino核心版本
5. **查看详细错误信息**: 启用详细编译输出

编译成功后，就可以上传代码到ESP32设备了！
