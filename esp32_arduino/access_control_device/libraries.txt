# ESP32智能门禁系统 - Arduino库依赖

## 必需的Arduino库

请在Arduino IDE的库管理器中安装以下库：

### 1. WiFi和网络通信
- **ESP32 Arduino Core** (通过开发板管理器安装)
  - 版本: 2.0.0 或更高
  - 提供WiFi和基础ESP32功能

### 2. WebSocket通信
- **WebSockets** by <PERSON>
  - 版本: 2.3.6 或更高
  - 用于与后端服务器的WebSocket通信

### 3. JSON数据处理
- **ArduinoJson** by Benoit Blanchon
  - 版本: 6.19.4 或更高
  - 用于JSON数据的序列化和反序列化

### 4. NFC/RFID模块
- **MFRC522** by GithubCommunity
  - 版本: 1.4.10 或更高
  - 用于RC522 NFC/RFID模块通信

### 5. OLED显示屏
- **Adafruit SSD1306** by Adafruit
  - 版本: 2.5.7 或更高
  - 用于SSD1306 OLED显示屏控制

- **Adafruit GFX Library** by Adafruit
  - 版本: 1.11.3 或更高
  - Adafruit SSD1306的依赖库，提供图形绘制功能

### 6. RGB LED控制
- **Adafruit NeoPixel** by Adafruit
  - 版本: 1.10.7 或更高
  - 用于WS2812B/NeoPixel RGB LED控制

### 7. 数据存储
- **Preferences** (ESP32内置库)
  - 用于在ESP32的NVS中存储设备状态

## 安装步骤

### 方法一：通过Arduino IDE库管理器
1. 打开Arduino IDE
2. 点击 工具 -> 管理库
3. 在搜索框中输入库名称
4. 点击安装对应版本

### 方法二：通过PlatformIO
如果使用PlatformIO，可以在platformio.ini中添加：

```ini
[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps = 
    links2004/WebSockets@^2.3.6
    bblanchon/ArduinoJson@^6.19.4
    miguelbalboa/MFRC522@^1.4.10
    adafruit/Adafruit SSD1306@^2.5.7
    adafruit/Adafruit GFX Library@^1.11.3
    adafruit/Adafruit NeoPixel@^1.10.7
```

## 硬件连接

### NFC模块 (RC522)
- VCC -> 3.3V
- GND -> GND
- RST -> GPIO2
- SDA -> GPIO5
- SCK -> GPIO18 (SPI SCK)
- MOSI -> GPIO23 (SPI MOSI)
- MISO -> GPIO19 (SPI MISO)

### OLED显示屏 (SSD1306)
- VCC -> 3.3V
- GND -> GND
- SDA -> GPIO42
- SCL -> GPIO41

### 继电器模块
- VCC -> 5V (或3.3V，根据继电器模块要求)
- GND -> GND
- IN -> GPIO14

### LED指示灯
- 绿色LED -> GPIO19 (通过220Ω电阻)
- 红色LED -> GPIO23 (通过220Ω电阻)

### RGB LED (WS2812B/NeoPixel)
- VCC -> 5V (或3.3V，根据LED规格)
- GND -> GND
- DIN -> GPIO48

### 手动按钮
- 一端 -> GPIO26
- 另一端 -> GND
- 内部上拉电阻已启用

## 注意事项

1. **电源要求**: 确保ESP32有足够的电源供应，特别是在驱动继电器时
2. **电压兼容**: 检查所有模块的工作电压，避免损坏
3. **引脚冲突**: 确保没有引脚冲突，特别是SPI引脚的使用
4. **库版本**: 使用推荐的库版本以确保兼容性

## 故障排除

### 编译错误
- 确保所有库都已正确安装
- 检查ESP32开发板是否已正确配置
- 验证库版本兼容性

### 运行时错误
- 检查硬件连接
- 验证配置文件中的引脚定义
- 查看串口输出的错误信息

### OLED显示问题
- 检查I2C地址是否正确 (通常是0x3C)
- 验证SDA和SCL引脚连接
- 确保OLED模块电源正常

### NFC读取问题
- 检查SPI连接
- 验证NFC模块电源
- 确保卡片与读卡器距离适当
